<!DOCTYPE html>
<html lang="en">
<head>
    <title>Dynamic Logo Test | <PERSON></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/ionicons.min.css">
    <link rel="stylesheet" href="css/pace.css">
    <link rel="stylesheet" href="css/design-system.css">
    <link rel="stylesheet" href="css/custom.css">
    <link rel="stylesheet" href="css/portfolio.css">
    
    <!-- Early theme application -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('portfolio-theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
        })();
    </script>
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            background: var(--bg-white);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        [data-theme="dark"] .test-container {
            background: #1f2937 !important;
            color: #f9fafb !important;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid var(--border-light);
            border-radius: 6px;
        }
        
        [data-theme="dark"] .test-section {
            border-color: #6b7280 !important;
            background: #374151 !important;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .test-pass {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .test-fail {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        
        [data-theme="dark"] .test-pass {
            background: #064e3b !important;
            color: #6ee7b7 !important;
        }
        
        [data-theme="dark"] .test-fail {
            background: #7f1d1d !important;
            color: #fca5a5 !important;
        }
        
        .logo-preview {
            display: flex;
            gap: 20px;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
        
        .logo-info {
            text-align: center;
            padding: 10px;
        }
        
        .current-theme {
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
        }
        
        .theme-switch-btn {
            background: var(--rust-accent);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .theme-switch-btn:hover {
            background: var(--kernel-dark);
            transform: translateY(-2px);
        }
        
        [data-theme="dark"] .theme-switch-btn {
            background: #60a5fa !important;
            color: #111827 !important;
        }
        
        [data-theme="dark"] .theme-switch-btn:hover {
            background: #fb923c !important;
        }
    </style>
</head>

<body>
    <div class="container">
        <header id="site-header">
            <div class="row">
                <div class="col-md-4 col-sm-5 col-xs-8">
                    <div class="logo">
                        <h1><a href="index.html"><b>Daniel</b> Orji</a></h1>
                    </div>
                </div>
                <div class="col-md-8 col-sm-7 col-xs-4">
                    <nav class="main-nav">
                        <ul class="nav navbar-nav navbar-right">
                            <li><a href="index.html">Home</a></li>
                            <li><a href="projects.html">Projects</a></li>
                            <li><a href="about.html">About</a></li>
                            <li><a href="contact.html">Contact</a></li>
                            <li><a href="support.html">Support</a></li>
                        </ul>
                    </nav>
                    <div id="header-theme-toggle">
                        <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                            <svg class="theme-toggle-icon sun-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            </svg>
                            <svg class="theme-toggle-icon moon-icon" fill="currentColor" viewBox="0 0 20 20" style="display: none;">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </header>
    </div>

    <!-- Hero Section with Dynamic Background Logo -->
    <section class="hero-section hero-with-logo-bg" role="banner" aria-labelledby="hero-title">
        <div class="hero-background-overlay"></div>
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h1 id="hero-title" class="hero-title">Dynamic Background Logo Test</h1>
                    <h2 class="hero-subtitle">Testing Theme-Based Background Logo Switching</h2>
                    <p class="hero-description">
                        This page tests the dynamic background logo switching functionality. The hero section background
                        should change automatically between light and dark logos when switching themes, maintaining
                        professional aesthetics and WCAG compliance.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Test Results Container -->
    <div class="test-container">
        <h2>Dynamic Logo Test Results</h2>
        
        <div class="current-theme">
            Current Theme: <span id="current-theme-display">Light</span>
        </div>
        
        <div class="text-center">
            <button class="theme-switch-btn" onclick="toggleThemeTest()">Toggle Theme & Test</button>
            <button class="theme-switch-btn" onclick="runAllTests()">Run All Tests</button>
        </div>
        
        <div class="test-section">
            <h3>Logo Visibility Test</h3>
            <div id="logo-visibility-result" class="test-result">Click "Run All Tests" to start</div>
            
            <div class="logo-preview">
                <div class="logo-info">
                    <h4>Light Logo</h4>
                    <div>Opacity: <span id="light-logo-opacity">-</span></div>
                    <div>Z-Index: <span id="light-logo-zindex">-</span></div>
                </div>
                <div class="logo-info">
                    <h4>Dark Logo</h4>
                    <div>Opacity: <span id="dark-logo-opacity">-</span></div>
                    <div>Z-Index: <span id="dark-logo-zindex">-</span></div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Theme Switching Test</h3>
            <div id="theme-switching-result" class="test-result">Click "Toggle Theme & Test" to start</div>
        </div>
        
        <div class="test-section">
            <h3>Accessibility Test</h3>
            <div id="accessibility-result" class="test-result">Click "Run All Tests" to start</div>
        </div>
        
        <div class="test-section">
            <h3>Performance Test</h3>
            <div id="performance-result" class="test-result">Click "Run All Tests" to start</div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery-2.1.3.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/script.js"></script>
    <script src="js/portfolio.js"></script>
    <script src="js/dark-mode-enforcer.js"></script>
    
    <script>
        let testResults = {
            logoVisibility: false,
            themeSwitching: false,
            accessibility: false,
            performance: false
        };
        
        function updateCurrentThemeDisplay() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            document.getElementById('current-theme-display').textContent = 
                currentTheme.charAt(0).toUpperCase() + currentTheme.slice(1);
        }
        
        function toggleThemeTest() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            // Use the existing theme toggle functionality
            if (window.themeToggle && window.themeToggle.setTheme) {
                window.themeToggle.setTheme(newTheme);
            } else {
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('portfolio-theme', newTheme);
            }
            
            setTimeout(() => {
                updateCurrentThemeDisplay();
                testThemeSwitching();
                updateLogoInfo();
            }, 300);
        }
        
        function testLogoVisibility() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const heroSection = document.querySelector('.hero-with-logo-bg');

            if (!heroSection) {
                document.getElementById('logo-visibility-result').innerHTML =
                    '<span class="test-fail">❌ Hero section with background logo not found</span>';
                return false;
            }

            const heroStyle = getComputedStyle(heroSection);
            const backgroundImage = heroStyle.backgroundImage;

            let passed = false;
            let message = '';

            if (currentTheme === 'light') {
                passed = backgroundImage.includes('light-logo.jpeg');
                message = passed ?
                    '✅ Light logo background visible' :
                    '❌ Light logo background not applied correctly';
            } else {
                passed = backgroundImage.includes('dark-logo.jpeg');
                message = passed ?
                    '✅ Dark logo background visible' :
                    '❌ Dark logo background not applied correctly';
            }

            document.getElementById('logo-visibility-result').innerHTML =
                `<span class="test-${passed ? 'pass' : 'fail'}">${message}<br>Background: ${backgroundImage}</span>`;

            testResults.logoVisibility = passed;
            return passed;
        }
        
        function testThemeSwitching() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const logoContainer = document.querySelector('.hero-logo-container');
            
            if (!logoContainer) {
                document.getElementById('theme-switching-result').innerHTML = 
                    '<span class="test-fail">❌ Logo container not found</span>';
                return false;
            }
            
            const containerStyle = getComputedStyle(logoContainer);
            const hasTransition = containerStyle.transition.includes('0.3s');
            
            const passed = hasTransition && (currentTheme === 'light' || currentTheme === 'dark');
            const message = passed ? 
                `✅ Theme switching working (${currentTheme} mode)` : 
                '❌ Theme switching not working properly';
            
            document.getElementById('theme-switching-result').innerHTML = 
                `<span class="test-${passed ? 'pass' : 'fail'}">${message}</span>`;
            
            testResults.themeSwitching = passed;
            return passed;
        }
        
        function testAccessibility() {
            const logoContainer = document.querySelector('.hero-logo-container');
            const lightLogo = document.querySelector('.hero-logo.light-logo');
            const darkLogo = document.querySelector('.hero-logo.dark-logo');
            
            let passed = true;
            let issues = [];
            
            // Check ARIA attributes
            if (!logoContainer.getAttribute('role') || !logoContainer.getAttribute('aria-label')) {
                passed = false;
                issues.push('Missing ARIA attributes on logo container');
            }
            
            // Check alt text
            if (!lightLogo.getAttribute('alt') || !darkLogo.getAttribute('alt')) {
                passed = false;
                issues.push('Missing alt text on logo images');
            }
            
            // Check loading attribute
            if (lightLogo.getAttribute('loading') !== 'eager' || darkLogo.getAttribute('loading') !== 'eager') {
                passed = false;
                issues.push('Logo images should use loading="eager"');
            }
            
            const message = passed ? 
                '✅ All accessibility requirements met' : 
                `❌ Accessibility issues: ${issues.join(', ')}`;
            
            document.getElementById('accessibility-result').innerHTML = 
                `<span class="test-${passed ? 'pass' : 'fail'}">${message}</span>`;
            
            testResults.accessibility = passed;
            return passed;
        }
        
        function testPerformance() {
            const startTime = performance.now();
            
            // Test logo switching performance
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            
            setTimeout(() => {
                document.documentElement.setAttribute('data-theme', currentTheme);
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                const passed = duration < 500; // Should complete within 500ms
                const message = passed ? 
                    `✅ Logo switching completed in ${duration.toFixed(2)}ms` : 
                    `❌ Logo switching too slow: ${duration.toFixed(2)}ms`;
                
                document.getElementById('performance-result').innerHTML = 
                    `<span class="test-${passed ? 'pass' : 'fail'}">${message}</span>`;
                
                testResults.performance = passed;
            }, 100);
            
            return true;
        }
        
        function updateLogoInfo() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const heroSection = document.querySelector('.hero-with-logo-bg');
            const overlay = document.querySelector('.hero-background-overlay');

            if (heroSection && overlay) {
                const heroStyle = getComputedStyle(heroSection);
                const overlayStyle = getComputedStyle(overlay);
                const backgroundImage = heroStyle.backgroundImage;

                // Update display based on current background
                if (currentTheme === 'light') {
                    document.getElementById('light-logo-opacity').textContent = backgroundImage.includes('light-logo') ? '1' : '0';
                    document.getElementById('light-logo-zindex').textContent = '1';
                    document.getElementById('dark-logo-opacity').textContent = '0';
                    document.getElementById('dark-logo-zindex').textContent = '0';
                } else {
                    document.getElementById('light-logo-opacity').textContent = '0';
                    document.getElementById('light-logo-zindex').textContent = '0';
                    document.getElementById('dark-logo-opacity').textContent = backgroundImage.includes('dark-logo') ? '1' : '0';
                    document.getElementById('dark-logo-zindex').textContent = '1';
                }
            }
        }
        
        function runAllTests() {
            console.log('🧪 Running Dynamic Logo Tests...');
            
            setTimeout(() => {
                testLogoVisibility();
                testThemeSwitching();
                testAccessibility();
                testPerformance();
                updateLogoInfo();
            }, 100);
        }
        
        // Initialize
        $(document).ready(function() {
            updateCurrentThemeDisplay();
            updateLogoInfo();
            
            // Listen for theme changes
            $(document).on('themeChanged', function() {
                setTimeout(() => {
                    updateCurrentThemeDisplay();
                    updateLogoInfo();
                }, 100);
            });
        });
    </script>
</body>
</html>
