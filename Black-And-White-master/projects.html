<!DOCTYPE html>
<html>
	<head>
		<title><PERSON> - <PERSON> | Linux Kernel Engineer</title>

		<!-- meta -->
		<meta charset="UTF-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1">
	    <meta name="description" content="Projects by <PERSON> - Linux Kernel Engineer. Showcasing C/Rust development, kernel modules, system programming, and cybersecurity projects.">
	    <meta name="keywords" content="Daniel Orji Projects, Linux Kernel Development, C Programming Projects, Rust Projects, System Programming, Cybersecurity">
	    <meta name="author" content="Daniel Orji">

	    <!-- Open Graph / Social Media -->
	    <meta property="og:type" content="website">
	    <meta property="og:title" content="Projects - Daniel Orji Linux Kernel Engineer">
	    <meta property="og:description" content="Portfolio of Linux kernel development and system programming projects">
	    <meta property="og:url" content="">
	    <meta property="og:image" content="">

	    <!-- css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<link rel="stylesheet" href="css/ionicons.min.css">
		<link rel="stylesheet" href="css/pace.css">
	    <link rel="stylesheet" href="css/design-system.css">
	    <link rel="stylesheet" href="css/custom.css">
	    <link rel="stylesheet" href="css/portfolio.css">

	    <!-- Early theme application to prevent FOUC -->
	    <script>
	        (function() {
	            const savedTheme = localStorage.getItem('portfolio-theme') || 'light';
	            document.documentElement.setAttribute('data-theme', savedTheme);
	        })();
	    </script>

	    <!-- js -->
	    <script src="js/jquery-2.1.3.min.js"></script>
	    <script src="js/bootstrap.min.js"></script>
	    <script src="js/pace.min.js"></script>
	    <script src="js/modernizr.custom.js"></script>
	</head>

	<body>
		<!-- Skip Navigation -->
		<a href="#main-content" class="skip-nav">Skip to main content</a>

		<div class="container">
			<header id="site-header" role="banner">
				<div class="row">
					<div class="col-md-4 col-sm-5 col-xs-8">
						<div class="logo">
							<h1><a href="index.html"><b>Daniel</b> Orji</a></h1>
						</div>
					</div><!-- col-md-4 -->
					<div class="col-md-8 col-sm-7 col-xs-4">
						<nav class="main-nav" role="navigation">
							<div class="navbar-header">
  								<button type="button" id="trigger-overlay" class="navbar-toggle">
    								<span class="ion-navicon"></span>
  								</button>
							</div>

							<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
  								<ul class="nav navbar-nav navbar-right">
    								<li class="cl-effect-11"><a href="index.html" data-hover="Home">Home</a></li>
    								<li class="cl-effect-11"><a href="projects.html" data-hover="Projects">Projects</a></li>
    								<li class="cl-effect-11"><a href="about.html" data-hover="About">About</a></li>
    								<li class="cl-effect-11"><a href="contact.html" data-hover="Contact">Contact</a></li>
    								<li class="cl-effect-11"><a href="support.html" data-hover="Support">Support</a></li>
  								</ul>
							</div><!-- /.navbar-collapse -->
						</nav>
						<div id="header-theme-toggle">
							<button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode" title="Toggle dark/light theme">
								<svg class="theme-toggle-icon sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
								</svg>
								<svg class="theme-toggle-icon moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
									<path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
								</svg>
							</button>
						</div>
					</div><!-- col-md-8 -->
				</div>
			</header>
		</div>

		<div class="content-body">
			<div class="container">
				<div class="row">
					<main id="main-content" class="col-md-12" role="main">
						<h1 class="page-title">My Projects</h1>
						
						<!-- Proxmox VE Mastery Project -->
						<article class="post post-1" id="proxmox">
							<header class="entry-header">
								<h1 class="entry-title">
									<a href="#proxmox">Proxmox VE Infrastructure Mastery</a>
								</h1>
								<div class="entry-meta">
									<span class="post-category"><a href="#">Infrastructure</a></span>
									<span class="post-date"><a href="#"><time class="entry-date" datetime="2024-10-01">October 2024 – Present</time></a></span>
									<span class="post-author"><a href="#">Daniel Orji</a></span>
									<span class="comments-link"><a href="">Ongoing Project</a></span>
								</div>
							</header>
							<div class="entry-content clearfix">
								<p>This is an <strong>ongoing freelance and private project</strong> where I am developing experimental skills in managing local IT infrastructure and services. I have deployed and managed traditional hosted services including DNS servers, VS Code remote development servers, and test infrastructure.</p>
								<p>The project focuses on <strong>improving server reliability, uptime, and service isolation</strong> for test deployments using Proxmox VE virtualization platform. This hands-on experience has enhanced my understanding of enterprise-level infrastructure management and virtualization technologies.</p>
								<div class="tech-badges">
									<span class="badge linux">Proxmox VE</span>
									<span class="badge linux">Linux</span>
									<span class="badge">DNS Management</span>
									<span class="badge cloud">Virtualization</span>
									<span class="badge">Service Isolation</span>
								</div>
								<div class="read-more cl-effect-14">
									<a href="#" class="more-link">View Technical Details <span class="meta-nav">→</span></a>
								</div>
							</div>
						</article>

						<!-- Customer Support Process Simulation -->
						<article class="post post-2" id="customer-support">
							<header class="entry-header">
								<h1 class="entry-title">
									<a href="#customer-support">Customer Support Process Simulation</a>
								</h1>
								<div class="entry-meta">
									<span class="post-category"><a href="#">Process Design</a></span>
									<span class="post-date"><a href="#"><time class="entry-date" datetime="2024-01-01">2024</time></a></span>
									<span class="post-author"><a href="#">Daniel Orji</a></span>
									<span class="comments-link"><a href="#">Simulation Project</a></span>
								</div>
							</header>
							<div class="entry-content clearfix">
								<p>Designed a comprehensive simulated customer support workflow to practice handling high-volume technical inquiries and warranty-related issues. This project involved creating detailed troubleshooting procedures and providing clear part specifications for mock service scenarios.</p>
								<p>The simulation helped develop skills in <strong>technical communication, process documentation, and customer service excellence</strong> while maintaining professional standards in technical support environments.</p>
								<div class="tech-badges">
									<span class="badge">Process Design</span>
									<span class="badge">Documentation</span>
									<span class="badge">Customer Service</span>
									<span class="badge">Technical Support</span>
								</div>
								<div class="read-more cl-effect-14">
									<a href="#" class="more-link">View Process Documentation <span class="meta-nav">→</span></a>
								</div>
							</div>
						</article>

						<!-- Technical Documentation & Training Material -->
						<article class="post post-3" id="documentation">
							<header class="entry-header">
								<h1 class="entry-title">
									<a href="#documentation">Technical Documentation & Training Materials</a>
								</h1>
								<div class="entry-meta">
									<span class="post-category"><a href="#" rel="category tag">Documentation</a></span>
									<span class="post-date"><a href="#"><time class="entry-date" datetime="2024-01-01">2024</time></a></span>
									<span class="post-author"><a href="#">Daniel Orji</a></span>
									<span class="comments-link"><a href="#">Knowledge Sharing</a></span>
								</div>
							</header>
							<div class="entry-content clearfix">
								<p>Developed comprehensive user-friendly guides and documentation for resolving common technical issues, ensuring consistency and clarity in customer communication. This project involved creating training materials for IT support scenarios and cybersecurity best practices.</p>
								<p>The documentation focuses on <strong>clear technical communication, standardized procedures, and knowledge transfer</strong> to support both technical teams and end users in various IT environments.</p>
								<div class="tech-badges">
									<span class="badge">Technical Writing</span>
									<span class="badge">Training Materials</span>
									<span class="badge">Knowledge Management</span>
									<span class="badge">IT Support</span>
								</div>
								<div class="read-more cl-effect-14">
									<a href="#" class="more-link">View Documentation Samples <span class="meta-nav">→</span></a>
								</div>
							</div>
						</article>

						<!-- Linux Kernel Learning Projects -->
						<article class="post post-4" id="kernel-learning">
							<header class="entry-header">
								<h1 class="entry-title">
									<a href="#kernel-learning">Linux Kernel Development Learning Path</a>
								</h1>
								<div class="entry-meta">
									<span class="post-category"><a href="#" rel="category tag">Kernel Development</a></span>
									<span class="post-date"><a href="#"><time class="entry-date" datetime="2024-01-01">2024 – Present</time></a></span>
									<span class="post-author"><a href="#">Daniel Orji</a></span>
									<span class="comments-link"><a href="#">Learning Project</a></span>
								</div>
							</header>
							<div class="entry-content clearfix">
								<p>Ongoing self-directed learning journey focused on Linux kernel fundamentals, device drivers, and low-level system programming. This includes studying kernel source code, understanding memory management, and exploring security hardening techniques.</p>
								<p>The learning path emphasizes <strong>C programming mastery, kernel architecture understanding, and practical system programming skills</strong> essential for contributing to Linux kernel development and Ubuntu system optimization.</p>
								<div class="tech-badges">
									<span class="badge linux">C Programming</span>
									<span class="badge linux">Kernel Development</span>
									<span class="badge rust">Device Drivers</span>
									<span class="badge">Memory Management</span>
									<span class="badge cloud">System Security</span>
								</div>
								<div class="read-more cl-effect-14">
									<a href="#" class="more-link">View Learning Progress <span class="meta-nav">→</span></a>
								</div>
							</div>
						</article>
					</main>
				</div>
			</div>
		</div>
		<footer id="site-footer">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<p class="copyright">&copy; 2024 Daniel Orji - Linux Kernel Engineer</p>
					</div>
				</div>
			</div>
		</footer>

		<!-- Mobile Menu -->
		<div class="overlay overlay-hugeinc">
			<button type="button" class="overlay-close"><span class="ion-ios-close-empty"></span></button>
			<nav>
				<ul>
					<li><a href="index.html">Home</a></li>
					<li><a href="projects.html">Projects</a></li>
					<li><a href="about.html">About</a></li>
					<li><a href="contact.html">Contact</a></li>
					<li><a href="support.html">Support</a></li>
				</ul>
			</nav>
		</div>

		<script src="js/script.js"></script>
		<script src="js/portfolio.js"></script>
		<script src="js/dark-mode-enforcer.js"></script>

	</body>
</html>
