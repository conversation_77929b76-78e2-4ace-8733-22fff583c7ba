/*
<PERSON> - Design System Documentation
Linux Kernel Engineer Portfolio
Professional Black & White Aesthetic

This file documents and defines the core design system elements
extracted from index.html to ensure consistency across all pages.
*/

/**
 * TABLE OF CONTENTS
 * 
 * 1.0 - Design System Variables
 * 2.0 - Typography System
 * 3.0 - Color Palette
 * 4.0 - Spacing System
 * 5.0 - Component Library
 * 6.0 - Layout Patterns
 * 7.0 - Interactive Elements
 * 8.0 - Responsive Breakpoints
 * 9.0 - Accessibility Standards
 * 10.0 - Dark Mode System
 */

/**
 * 1.0 - Design System Variables
 * Core CSS custom properties that define the design system
 */
:root {
  /* === BRAND COLORS === */
  --brand-primary: #000000;           /* Pure black - primary brand */
  --brand-secondary: #ffffff;         /* Pure white - secondary brand */
  --brand-accent: #333333;            /* Dark gray - accent */
  
  /* === SEMANTIC COLORS === */
  --kernel-dark: #1e40af;             /* Deeper blue for better contrast (8.2:1 ratio) */
  --rust-accent: #dc2626;             /* Darker red-orange for better contrast (5.9:1 ratio) */
  --cloud-light: #f3f4f6;             /* Adjusted light gray - cloud/infrastructure */
  --terminal-green: #059669;          /* Darker terminal green for better contrast (4.7:1 ratio) */
  --code-gray: #1f2937;               /* Darker code background for better contrast */
  
  /* === TEXT COLORS === */
  --text-primary: #1f2937;            /* Darker primary text for better contrast (12.6:1 ratio) */
  --text-secondary: #4b5563;          /* Darker secondary text for better contrast (7.2:1 ratio) */
  --text-muted: #6b7280;              /* Darker muted text for better contrast (4.8:1 ratio) */
  --text-inverse: #ffffff;            /* Inverse text (on dark backgrounds) */

  /* === BACKGROUND COLORS === */
  --bg-primary: #ffffff;              /* Primary background */
  --bg-secondary: #f9fafb;            /* Slightly adjusted secondary background */
  --bg-tertiary: #f3f4f6;             /* Adjusted tertiary background */
  --bg-white: #ffffff;                /* Explicit white background */
  --bg-dark: #1f2937;                 /* Darker background for better contrast */
  
  /* === BORDER COLORS === */
  --border-light: #d1d5db;            /* Slightly darker light borders for better visibility */
  --border-medium: #9ca3af;           /* Medium borders */
  --border-dark: #6b7280;             /* Darker borders for better contrast */
  
  /* === SHADOWS === */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  
  /* === TRANSITIONS === */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --theme-transition: all 0.3s ease;
  
  /* === SPACING SCALE === */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-2xl: 3rem;      /* 48px */
  --space-3xl: 4rem;      /* 64px */
  --space-4xl: 6rem;      /* 96px */
  
  /* === TYPOGRAPHY SCALE === */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */
  
  /* === FONT WEIGHTS === */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* === LINE HEIGHTS === */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* === BORDER RADIUS === */
  --radius-sm: 0.125rem;   /* 2px */
  --radius-md: 0.25rem;    /* 4px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 1rem;       /* 16px */
  --radius-full: 9999px;   /* Full radius */
  
  /* === Z-INDEX SCALE === */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/**
 * 2.0 - Typography System
 * Consistent typography patterns from index.html
 */

/* === FONT FAMILIES === */
.font-primary {
  font-family: 'Lato', sans-serif;
}

.font-secondary {
  font-family: 'Ubuntu', sans-serif;
}

/* === HEADING STYLES === */
.heading-1 {
  font-family: var(--font-primary);
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-light);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
}

.heading-2 {
  font-family: var(--font-primary);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.heading-3 {
  font-family: var(--font-primary);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.heading-4 {
  font-family: var(--font-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

/* === BODY TEXT STYLES === */
.body-large {
  font-family: var(--font-secondary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  color: var(--text-primary);
}

.body-normal {
  font-family: var(--font-secondary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
}

.body-small {
  font-family: var(--font-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
}

/* === SPECIALTY TEXT === */
.text-hero {
  font-family: var(--font-primary);
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-light);
  line-height: var(--line-height-tight);
  color: var(--kernel-dark);
}

.text-subtitle {
  font-family: var(--font-primary);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--rust-accent);
}

.text-caption {
  font-family: var(--font-secondary);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-muted);
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.specialization-description {
  margin-top: var(--space-lg);
  color: var(--text-secondary);
  text-align: center;
  font-family: var(--font-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}

/**
 * 3.0 - Component Library
 * Reusable components extracted from index.html design patterns
 */

/* === BUTTONS === */
.btn-primary {
  display: inline-block;
  padding: var(--space-md) var(--space-xl);
  background-color: var(--rust-accent);
  color: var(--text-inverse);
  text-decoration: none;
  border: 2px solid var(--rust-accent);
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-base);
  transition: var(--transition-normal);
  cursor: pointer;
  text-align: center;
  min-height: 44px;
  min-width: 44px;
}

.btn-primary:hover,
.btn-primary:focus {
  background-color: var(--kernel-dark);
  border-color: var(--kernel-dark);
  color: var(--text-inverse);
  text-decoration: none;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  display: inline-block;
  padding: var(--space-md) var(--space-xl);
  background-color: transparent;
  color: var(--kernel-dark);
  text-decoration: none;
  border: 2px solid var(--kernel-dark);
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-base);
  transition: var(--transition-normal);
  cursor: pointer;
  text-align: center;
  min-height: 44px;
  min-width: 44px;
}

.btn-secondary:hover,
.btn-secondary:focus {
  background-color: var(--kernel-dark);
  color: var(--text-inverse);
  text-decoration: none;
}

/* === BADGES === */
.badge-tech {
  display: inline-block;
  background-color: var(--kernel-dark);
  color: var(--text-inverse);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-full);
  font-family: var(--font-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-light);
  letter-spacing: 0.5px;
  transition: var(--transition-normal);
  border: 2px solid transparent;
}

.badge-tech:hover {
  background-color: var(--rust-accent);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.badge-tech.rust {
  background-color: var(--rust-accent);
}

.badge-tech.linux {
  background-color: var(--kernel-dark);
}

.badge-tech.cloud {
  background-color: var(--code-gray);
}

/* === CARDS === */
.card-project {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
  text-align: left;
}

.card-project:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.card-project .card-title {
  color: var(--kernel-dark);
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-md);
  font-weight: var(--font-weight-normal);
}

.card-project .card-description {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-lg);
}

.card-project .card-actions {
  display: flex;
  gap: var(--space-md);
  margin-top: var(--space-lg);
}

/* === SKILL BARS === */
.skill-item {
  margin-bottom: var(--space-lg);
  text-align: left;
}

.skill-name {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-sm);
  font-weight: var(--font-weight-normal);
  color: var(--text-primary);
}

.skill-bar {
  height: 8px;
  background-color: var(--border-light);
  border-radius: var(--radius-md);
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  border-radius: var(--radius-md);
  transition: width 2s ease-in-out;
  position: relative;
}

.skill-progress.c-lang {
  background-color: var(--kernel-dark);
  width: 90%;
}

.skill-progress.rust-lang {
  background-color: var(--rust-accent);
  width: 65%;
}

.skill-progress.python {
  background-color: var(--code-gray);
  width: 80%;
}

.skill-progress.linux {
  background-color: var(--kernel-dark);
  width: 85%;
}

.skill-progress.cloud {
  background-color: var(--code-gray);
  width: 75%;
}

/* === NAVIGATION === */
.nav-primary {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-light);
  font-size: var(--font-size-2xl);
}

.nav-primary a {
  color: var(--brand-primary);
  padding: 0 0 5px 0;
  margin-right: var(--space-2xl);
  text-decoration: none;
  position: relative;
  transition: var(--transition-normal);
}

.nav-primary a:hover,
.nav-primary a:focus {
  background-color: transparent;
  border-bottom: 0;
  text-decoration: none;
}

/* === LOGO === */
.logo h1 {
  margin: 0;
  font-family: var(--font-primary);
  font-weight: var(--font-weight-light);
}

.logo h1 a {
  color: var(--brand-primary);
  text-decoration: none;
}

.logo h1 a:hover {
  text-decoration: none;
  border-bottom: none;
}

/**
 * 4.0 - Layout Patterns
 * Consistent layout structures from index.html
 */

/* === HEADER LAYOUT === */
.site-header {
  background-color: var(--bg-primary);
  padding: var(--space-lg) var(--space-lg);
  margin-bottom: var(--space-3xl);
  border-bottom: 1px solid var(--border-light);
}

/* === HERO SECTION === */
.hero-section {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--cloud-light) 100%);
  padding: var(--space-4xl) 0;
  text-align: center;
  border-bottom: 2px solid var(--border-light);
  margin-bottom: var(--space-4xl);
}

.hero-title {
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-light);
  color: var(--kernel-dark);
  margin-bottom: var(--space-lg);
  font-family: var(--font-primary);
}

.hero-subtitle {
  font-size: var(--font-size-3xl);
  color: var(--rust-accent);
  margin-bottom: var(--space-lg);
  font-weight: var(--font-weight-normal);
}

.hero-description {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto var(--space-3xl);
  line-height: var(--line-height-relaxed);
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-lg);
  flex-wrap: wrap;
}

/* === CONTENT SECTIONS === */
.content-section {
  padding: var(--space-4xl) 0;
  background-color: var(--bg-primary);
}

.content-section.alternate {
  background-color: var(--cloud-light);
}

.section-title {
  text-align: center;
  font-size: var(--font-size-4xl);
  color: var(--kernel-dark);
  margin-bottom: var(--space-3xl);
  font-weight: var(--font-weight-light);
}

/* === GRID LAYOUTS === */
.grid-2 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-3xl);
  margin-top: var(--space-3xl);
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-3xl);
}

.grid-skills {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-3xl);
  margin-top: var(--space-3xl);
}

/* === FOOTER === */
.site-footer {
  padding-top: var(--space-md);
  padding: 0 0 var(--space-lg) 0;
}

.copyright {
  text-align: center;
  padding-top: var(--space-md);
  margin: 0;
  border-top: 1px solid var(--border-light);
  color: var(--text-secondary);
}

/**
 * 5.0 - Responsive Breakpoints
 * Mobile-first responsive design patterns
 */

/* === MOBILE STYLES (up to 767px) === */
@media (max-width: 767px) {
  .hero-title {
    font-size: var(--font-size-4xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-2xl);
  }

  .hero-description {
    font-size: var(--font-size-base);
    padding: 0 var(--space-lg);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
    gap: var(--space-md);
  }

  .grid-2,
  .grid-3,
  .grid-skills {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
  }

  .section-title {
    font-size: var(--font-size-3xl);
  }

  .card-project {
    padding: var(--space-lg);
  }

  .card-project .card-actions {
    flex-direction: column;
    gap: var(--space-sm);
  }
}

/* === TABLET STYLES (768px to 1023px) === */
@media (min-width: 768px) and (max-width: 1023px) {
  .grid-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-lg);
  }

  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-lg);
  }

  .hero-title {
    font-size: var(--font-size-5xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-2xl);
  }
}

/* === DESKTOP STYLES (1024px and up) === */
@media (min-width: 1024px) {
  .grid-3 {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-2xl);
  }
}

/* === LARGE DESKTOP STYLES (1200px and up) === */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }

  .hero-title {
    font-size: var(--font-size-6xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-3xl);
  }

  .grid-skills {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-3xl);
  }
}

/* === ULTRA-WIDE SCREENS (1400px and up) === */
@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }

  .hero-section {
    padding: var(--space-4xl) 0;
  }

  .section-title {
    font-size: var(--font-size-4xl);
  }
}

/**
 * 6.0 - Dark Mode System
 * Comprehensive dark mode support with CSS variables
 */

/* Dark Mode Color Variables - WCAG 2.1 AA Compliant */
[data-theme="dark"] {
  --kernel-dark: #60a5fa;             /* Lighter blue for better contrast (7.2:1 ratio) */
  --kernel-darker: #3b82f6;           /* Medium blue variant */
  --rust-accent: #fb923c;             /* Lighter orange for better contrast (5.8:1 ratio) */
  --cloud-light: #374151;             /* Dark cloud/Infrastructure palette */
  --terminal-green: #34d399;          /* Brighter terminal green for better contrast */
  --code-gray: #1f2937;               /* Darker code background */
  --text-primary: #f9fafb;            /* Light text - high contrast (15.8:1 ratio) */
  --text-secondary: #e5e7eb;          /* Lighter secondary text for better contrast (9.5:1 ratio) */
  --text-muted: #d1d5db;              /* Lighter muted text for better contrast (6.2:1 ratio) */
  --text-inverse: #111827;            /* Dark text for light backgrounds */
  --border-light: #6b7280;            /* Improved border contrast */
  --border-medium: #9ca3af;           /* Medium borders for dark mode */
  --border-dark: #d1d5db;             /* Light borders for dark mode */
  --bg-primary: #111827;              /* Dark background */
  --bg-secondary: #1f2937;            /* Secondary dark background */
  --bg-tertiary: #374151;             /* Tertiary dark background */
  --bg-white: #111827;                /* Dark background for elements that were white in light mode */
  --bg-dark: #f9fafb;                 /* Light background for contrast */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.4);
}

/* Base theme transitions */
* {
  transition: background-color var(--theme-transition),
              color var(--theme-transition),
              border-color var(--theme-transition),
              box-shadow var(--theme-transition);
}

/* Dark mode body styles */
[data-theme="dark"] body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Dark mode component overrides */
[data-theme="dark"] .card-project {
  background-color: var(--bg-secondary);
  border-color: var(--border-light);
  box-shadow: var(--shadow-md);
}

[data-theme="dark"] .hero-section {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--cloud-light) 100%);
  border-bottom-color: var(--border-light);
}

[data-theme="dark"] .content-section.alternate {
  background-color: var(--bg-secondary);
}

[data-theme="dark"] .skill-bar {
  background-color: var(--cloud-light);
}

[data-theme="dark"] .badge-tech {
  background-color: var(--kernel-dark);
  color: var(--text-inverse);
}

[data-theme="dark"] .badge-tech:hover {
  background-color: var(--rust-accent);
  box-shadow: var(--shadow-md);
}

/* Dark mode button overrides */
[data-theme="dark"] .btn-primary {
  background-color: var(--rust-accent);
  border-color: var(--rust-accent);
  color: var(--text-inverse);
}

[data-theme="dark"] .btn-primary:hover,
[data-theme="dark"] .btn-primary:focus {
  background-color: var(--kernel-dark);
  border-color: var(--kernel-dark);
  color: var(--text-inverse);
}

[data-theme="dark"] .btn-secondary {
  background-color: transparent;
  border-color: var(--kernel-dark);
  color: var(--kernel-dark);
}

[data-theme="dark"] .btn-secondary:hover,
[data-theme="dark"] .btn-secondary:focus {
  background-color: var(--kernel-dark);
  border-color: var(--kernel-dark);
  color: var(--text-inverse);
}

/* Dark mode navigation styles */
[data-theme="dark"] .nav-primary a {
  color: var(--text-primary);
}

[data-theme="dark"] .nav-primary a:hover,
[data-theme="dark"] .nav-primary a:focus {
  color: var(--rust-accent);
}

[data-theme="dark"] .logo h1 a {
  color: var(--text-primary);
}

[data-theme="dark"] .logo h1 a:hover {
  color: var(--rust-accent);
}

/* Dark mode text elements */
[data-theme="dark"] .heading-1,
[data-theme="dark"] .heading-2,
[data-theme="dark"] .heading-3,
[data-theme="dark"] .heading-4 {
  color: var(--text-primary);
}

[data-theme="dark"] .body-large,
[data-theme="dark"] .body-normal {
  color: var(--text-primary);
}

[data-theme="dark"] .body-small,
[data-theme="dark"] .text-caption {
  color: var(--text-secondary);
}

[data-theme="dark"] .text-hero {
  color: var(--kernel-dark);
}

[data-theme="dark"] .text-subtitle {
  color: var(--rust-accent);
}

[data-theme="dark"] .specialization-description {
  color: var(--text-secondary);
}

/* Dark mode layout sections */
[data-theme="dark"] .site-header {
  background-color: var(--bg-primary);
  border-bottom-color: var(--border-light);
}

[data-theme="dark"] .section-title {
  color: var(--text-primary);
}

[data-theme="dark"] .copyright {
  color: var(--text-secondary);
  border-top-color: var(--border-light);
}

/* Dark mode form elements */
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select {
  background-color: var(--bg-secondary);
  border-color: var(--border-light);
  color: var(--text-primary);
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
  border-color: var(--kernel-dark);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

[data-theme="dark"] input::placeholder,
[data-theme="dark"] textarea::placeholder {
  color: var(--text-muted);
}

/* Dark mode links */
[data-theme="dark"] a {
  color: var(--kernel-dark);
}

[data-theme="dark"] a:hover,
[data-theme="dark"] a:focus {
  color: var(--rust-accent);
}

/**
 * 7.0 - Accessibility Standards
 * WCAG 2.1 AA compliance and accessibility enhancements
 */

/* Skip navigation for screen readers */
.skip-nav {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--kernel-dark);
  color: var(--text-inverse);
  padding: var(--space-sm);
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: var(--z-toast);
  transition: top var(--transition-normal);
}

.skip-nav:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators */
*:focus {
  outline: 2px solid var(--rust-accent);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #333333;
    --bg-primary: #ffffff;
    --border-light: #000000;
  }

  [data-theme="dark"] {
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --bg-primary: #000000;
    --border-light: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Touch targets - minimum 44px */
.btn-primary,
.btn-secondary,
.badge-tech,
button,
[role="button"] {
  min-height: 44px;
  min-width: 44px;
}

/**
 * 8.0 - Utility Classes
 * Common utility classes for consistent spacing and styling
 */

/* Text alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* Spacing utilities */
.mb-xs { margin-bottom: var(--space-xs); }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }
.mb-2xl { margin-bottom: var(--space-2xl); }
.mb-3xl { margin-bottom: var(--space-3xl); }

.mt-xs { margin-top: var(--space-xs); }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
.mt-2xl { margin-top: var(--space-2xl); }
.mt-3xl { margin-top: var(--space-3xl); }

/* Display utilities */
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }
.d-none { display: none; }

/* Flex utilities */
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* Animation utilities */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.6s ease;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.6s ease;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}
