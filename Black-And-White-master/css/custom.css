/*
Theme Name: Black & White
Theme URI: http://themewagon.com/demo/Black-And-White/
Author: ThemeWagon
Author URI: http://themewagon.com/
Description: Minimal Blogging Theme
Version: 1.0
License: GNU General Public License v3 or later
License URI: http://www.gnu.org/licenses/gpl-3.0.html
Tags: black, white, two-columns, right-sidebar, responsive-layout, accessibility-ready
Text Domain: blackandwhite
*/


/**
 * Table of Contents
 *
 * 1.0 - Google Font
 * 2.0 - General Elements
 * 3.0 - Site Header
 *   3.1 - Logo
 *   3.2 - Main Navigation
 *     3.2.1 - Main Nav CSS 3 Hover Effect
 * 4.0 - Home/Blog
 *   4.1 - Read More Button CSS 3 style
 * 5.0 - Widget
 * 6.0 - Footer
 * 7.0 - Header Search Bar
 * 8.0 - Mobile Menu
 * 9.0 - Contact Page Social
 * 10.0 - Contact Form
 * 11.0 - Media Query
 */

/**
 * 1.0 - Google Font
 */


@import url(http://fonts.googleapis.com/css?family=Lato:300,400);
@import url(http://fonts.googleapis.com/css?family=Ubuntu:300,400);

/**
 * 2.0 - General Elements
 */

* {outline: none;}

h1,
h2,
h3,
h4,
h5,
h6 {
	margin-top: 0;
}

b {
	font-weight: 400;
}

a {
	color: var(--text-primary, #333);
}

a:hover, a:focus {
	text-decoration: none;
	color: var(--text-primary, #000);
}

::selection {
	background-color: var(--cloud-light, #eee);
}

body {
	color: var(--text-secondary, #444);
	font-family: 'Lato', sans-serif;
	background-color: var(--bg-white, #ffffff);
	transition: background-color var(--theme-transition, 0.3s ease), color var(--theme-transition, 0.3s ease);
}

p {
	font-family: 'Ubuntu', sans-serif;
	word-spacing: 1px;
	letter-spacing: 0.01em;
	font-size: 16px;
	line-height: 25px;
	color: var(--text-secondary, #444);
}

#single p,
#page p {
	margin-bottom: 25px;
}

.page-title {
	text-align: center;
	color: var(--text-primary, #333);
}
h1.entry-title a:hover {
	color: var(--text-secondary, gray);
}

.title {
	margin-bottom: 30px;
	color: var(--text-primary, #333);
}

figure {
	margin-bottom: 25px;
}

.img-responsive-center img {
	margin: 0 auto;
}

.height-40px {
	margin-bottom: 40px;
}

/* Dark Mode Overrides for General Elements */
[data-theme="dark"] a {
	color: var(--text-primary);
}

[data-theme="dark"] a:hover,
[data-theme="dark"] a:focus {
	color: var(--kernel-dark);
}

[data-theme="dark"] ::selection {
	background-color: var(--cloud-light);
}

[data-theme="dark"] body {
	color: var(--text-primary);
	background-color: var(--bg-white);
}

[data-theme="dark"] p {
	color: var(--text-secondary);
}

[data-theme="dark"] .page-title {
	color: var(--text-primary);
}

[data-theme="dark"] h1.entry-title a:hover {
	color: var(--text-secondary);
}

[data-theme="dark"] .title {
	color: var(--text-primary);
}

/* Dark Mode Overrides for Standard HTML Elements */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
	color: var(--text-primary);
}

[data-theme="dark"] strong,
[data-theme="dark"] b {
	color: var(--text-primary);
}

[data-theme="dark"] em,
[data-theme="dark"] i {
	color: var(--text-secondary);
}

[data-theme="dark"] ul,
[data-theme="dark"] ol,
[data-theme="dark"] li {
	color: var(--text-secondary);
}

/**
 * 3.0 - Site Header
 */

#site-header {
	background-color: var(--bg-white, #FFF);
	padding: 25px 20px;
	margin-bottom: 40px;
	border-bottom: 1px solid var(--border-light, #e7e7e7);
	transition: background-color var(--theme-transition, 0.3s ease), border-color var(--theme-transition, 0.3s ease);
}

/**
 * 3.1 - Logo
 */

.logo h1 a {
    color: var(--text-primary, #000);
    transition: color var(--theme-transition, 0.3s ease);
}

.logo h1 a:hover {
    text-decoration: none;
    border-bottom: none;
    color: var(--kernel-dark, #000);
}

.logo h1 {
	margin: 0;
	font-family: 'Lato', sans-serif;
	font-weight: 300;
}

/* Dark Mode Overrides for Site Header */
[data-theme="dark"] #site-header {
	background-color: var(--bg-white);
	border-bottom-color: var(--border-light);
}

[data-theme="dark"] .logo h1 a {
    color: var(--text-primary);
}

[data-theme="dark"] .logo h1 a:hover {
    color: var(--kernel-dark);
}

[data-theme="dark"] .logo h1 a b {
    color: var(--text-primary);
}

[data-theme="dark"] .logo h1 {
    color: var(--text-primary);
}

/**
 * 3.2 - Main Navigation
 */

.main-nav {
	margin-top: 11px;
	max-width: 95%;
}

.main-nav a {
	color: var(--text-primary, #000000) !important;
	padding: 0 0 5px 0 !important;
	margin-right: 30px;
	font-family: 'Lato', sans-serif;
	font-weight: 300;
	font-size: 24px;
	transition: color var(--theme-transition, 0.3s ease) !important;
}

.main-nav a:active,
.main-nav a:focus,
.main-nav a:hover {
	background-color: transparent !important;
	border-bottom: 0;
	color: var(--kernel-dark, #000) !important;
}

.navbar-toggle {
    margin: 0;
    border: 0;
    padding: 0;
    margin-right: 25px;
    background: transparent;
}

.navbar-toggle span {
    font-size: 2em;
    color: var(--text-primary, #000);
    transition: color var(--theme-transition, 0.3s ease);
}

/* Dark Mode Overrides for Main Navigation */
[data-theme="dark"] .main-nav a {
	color: var(--text-primary) !important;
}

[data-theme="dark"] .main-nav a:active,
[data-theme="dark"] .main-nav a:focus,
[data-theme="dark"] .main-nav a:hover {
	color: var(--kernel-dark) !important;
}

[data-theme="dark"] .navbar-toggle span {
    color: var(--text-primary);
}

/**
 * 3.2.1 - Main Nav CSS 3 Hover Effect
 */

.cl-effect-11 a {
	padding: 10px 0;
	color: var(--kernel-dark, #0972b4);
	text-shadow: none;
	transition: color var(--theme-transition, 0.3s ease);
}

.cl-effect-11 a::before {
	position: absolute;
	top: 0;
	left: 0;
	overflow: hidden;
	padding: 0 0 5px 0 !important;
	max-width: 0;
	border-bottom: 1px solid var(--text-primary, #000);
	color: var(--text-primary, #000);
	content: attr(data-hover);
	-webkit-transition: max-width 0.5s, border-color var(--theme-transition, 0.3s ease), color var(--theme-transition, 0.3s ease);
	-moz-transition: max-width 0.5s, border-color var(--theme-transition, 0.3s ease), color var(--theme-transition, 0.3s ease);
	transition: max-width 0.5s, border-color var(--theme-transition, 0.3s ease), color var(--theme-transition, 0.3s ease);
}

.cl-effect-11 a:hover::before,
.cl-effect-11 a:focus::before {
	max-width: 100%;
}

/* Dark Mode Overrides for Nav Hover Effect */
[data-theme="dark"] .cl-effect-11 a {
	color: var(--kernel-dark);
}

[data-theme="dark"] .cl-effect-11 a::before {
	border-bottom-color: var(--text-primary);
	color: var(--text-primary);
}

/**
 * 4.0 - Home/Blog
 */

.content-body {
	padding-bottom: 4em;
	background-color: var(--bg-white, transparent);
	transition: background-color var(--theme-transition, 0.3s ease);
}

.post {
	background: var(--bg-white, #fff);
	padding: 30px 30px 0;
	transition: background-color var(--theme-transition, 0.3s ease);
}

.entry-title {
	text-align: center;
	font-size: 1.9em;
	margin-bottom: 20px;
	line-height: 1.6;
	padding: 10px 20px 0;
	color: var(--text-primary, #333);
	transition: color var(--theme-transition, 0.3s ease);
}

.entry-meta {
	text-align: center;
	color: var(--text-muted, #DDDDDD);
	font-size: 13px;
	letter-spacing: 1px;
	margin-bottom: 30px;
	transition: color var(--theme-transition, 0.3s ease);
}

.entry-content {
	font-size: 18px;
	line-height: 1.9;
	font-weight: 300;
	color: var(--text-primary, #000);
	transition: color var(--theme-transition, 0.3s ease);
}

.post-category,
.post-date,
.post-author {
	position: relative;
	padding-right: 15px;
}

.post-category::after,
.post-date::after,
.post-author::after {
	position: absolute;
	content: '.';
	color: var(--text-primary, #000);
	font-size: 30px;
	top: -22px;
	right: 1px;
	transition: color var(--theme-transition, 0.3s ease);
}

/* Dark Mode Overrides for Home/Blog */
[data-theme="dark"] .content-body {
	background-color: var(--bg-white);
}

[data-theme="dark"] .post {
	background: var(--bg-white);
}

[data-theme="dark"] .entry-title {
	color: var(--text-primary);
}

[data-theme="dark"] .entry-meta {
	color: var(--text-muted);
}

[data-theme="dark"] .entry-content {
	color: var(--text-primary);
}

[data-theme="dark"] .post-category::after,
[data-theme="dark"] .post-date::after,
[data-theme="dark"] .post-author::after {
	color: var(--text-primary);
}

/* Dark Mode Overrides for Entry Content */
[data-theme="dark"] .entry-content {
	color: var(--text-secondary);
}

[data-theme="dark"] .entry-content h1,
[data-theme="dark"] .entry-content h2,
[data-theme="dark"] .entry-content h3,
[data-theme="dark"] .entry-content h4,
[data-theme="dark"] .entry-content h5,
[data-theme="dark"] .entry-content h6 {
	color: var(--text-primary);
}

[data-theme="dark"] .entry-content p {
	color: var(--text-secondary);
}

[data-theme="dark"] .entry-content strong,
[data-theme="dark"] .entry-content b {
	color: var(--text-primary);
}

[data-theme="dark"] .entry-content ul,
[data-theme="dark"] .entry-content ol,
[data-theme="dark"] .entry-content li {
	color: var(--text-secondary);
}

/**
 * 4.1 - Read More Button CSS 3 style
 */

.read-more {
	font-family: 'Ubuntu', sans-serif;
	font-weight: 400;
	word-spacing: 1px;
	letter-spacing: 0.01em;
	text-align: center;
	margin-top: 20px;
}

.cl-effect-14 a {
	padding: 0 20px;
	height: 45px;
	line-height: 45px;
	position: relative;
	display: inline-block;
	margin: 15px 25px;
	letter-spacing: 1px;
	font-weight: 400;
	text-shadow: 0 0 1px rgba(255,255,255,0.3);
	color: var(--text-primary, #333);
	transition: color var(--theme-transition, 0.3s ease);
}

.cl-effect-14 a::before,
.cl-effect-14 a::after {
	position: absolute;
	width: 45px;
	height: 1px;
	background: var(--border-light, #C3C3C3);
	content: '';
	-webkit-transition: all 0.3s, background-color var(--theme-transition, 0.3s ease);
	-moz-transition: all 0.3s, background-color var(--theme-transition, 0.3s ease);
	transition: all 0.3s, background-color var(--theme-transition, 0.3s ease);
	pointer-events: none;
}

.cl-effect-14 a::before {
	top: 0;
	left: 0;
	-webkit-transform: rotate(90deg);
	-moz-transform: rotate(90deg);
	transform: rotate(90deg);
	-webkit-transform-origin: 0 0;
	-moz-transform-origin: 0 0;
	transform-origin: 0 0;
}

.cl-effect-14 a::after {
	right: 0;
	bottom: 0;
	-webkit-transform: rotate(90deg);
	-moz-transform: rotate(90deg);
	transform: rotate(90deg);
	-webkit-transform-origin: 100% 0;
	-moz-transform-origin: 100% 0;
	transform-origin: 100% 0;
}

.cl-effect-14 a:hover::before,
.cl-effect-14 a:hover::after,
.cl-effect-14 a:focus::before,
.cl-effect-14 a:focus::after {
	background: var(--text-primary, #000);
}

.cl-effect-14 a:hover::before,
.cl-effect-14 a:focus::before {
	left: 50%;
	-webkit-transform: rotate(0deg) translateX(-50%);
	-moz-transform: rotate(0deg) translateX(-50%);
	transform: rotate(0deg) translateX(-50%);
}

.cl-effect-14 a:hover::after,
.cl-effect-14 a:focus::after {
	right: 50%;
	-webkit-transform: rotate(0deg) translateX(50%);
	-moz-transform: rotate(0deg) translateX(50%);
	transform: rotate(0deg) translateX(50%);
}

/* Dark Mode Overrides for Read More Button */
[data-theme="dark"] .cl-effect-14 a {
	color: var(--text-primary);
	text-shadow: 0 0 1px rgba(0,0,0,0.3);
}

[data-theme="dark"] .cl-effect-14 a::before,
[data-theme="dark"] .cl-effect-14 a::after {
	background: var(--border-light);
}

[data-theme="dark"] .cl-effect-14 a:hover::before,
[data-theme="dark"] .cl-effect-14 a:hover::after,
[data-theme="dark"] .cl-effect-14 a:focus::before,
[data-theme="dark"] .cl-effect-14 a:focus::after {
	background: var(--text-primary);
}


/**
 * 5.0 - Widget
 */

.widget {
    background: var(--bg-white, #fff);
    padding: 30px 0 0;
    transition: background-color var(--theme-transition, 0.3s ease);
}

.widget-title {
	font-size: 1.5em;
	margin-bottom: 20px;
	line-height: 1.6;
	padding: 10px 0 0;
	font-weight: 400;
	color: var(--text-primary, #333);
	transition: color var(--theme-transition, 0.3s ease);
}

.widget-recent-posts ul {
    padding: 0;
    margin: 0;
    padding-left: 20px;
}

.widget-recent-posts ul li {
    list-style-type: none;
    position: relative;
    line-height: 170%;
    margin-bottom: 10px;
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.widget-recent-posts ul li::before {
    content: '\f3d3';
    font-family: "Ionicons";
    position: absolute;
    left: -17px;
    top: 3px;
    font-size: 16px;
    color: var(--text-primary, #000);
    transition: color var(--theme-transition, 0.3s ease);
}

.widget-archives ul {
    padding: 0;
    margin: 0;
    padding-left: 25px;
}

.widget-archives ul li {
    list-style-type: none;
    position: relative;
    line-height: 170%;
    margin-bottom: 10px;
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.widget-archives ul li::before {
    content: '\f3f3';
    font-family: "Ionicons";
    position: absolute;
    left: -25px;
    top: 1px;
    font-size: 16px;
    color: var(--text-primary, #000);
    transition: color var(--theme-transition, 0.3s ease);
}

.widget-category ul {
    padding: 0;
    margin: 0;
    padding-left: 25px;
}

.widget-category ul li {
    list-style-type: none;
    position: relative;
    line-height: 170%;
    margin-bottom: 10px;
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.widget-category ul li::before {
    content: '\f3fe';
    font-family: "Ionicons";
    position: absolute;
    left: -25px;
    top: 1px;
    font-size: 18px;
    color: var(--text-primary, #000);
    transition: color var(--theme-transition, 0.3s ease);
}

/* Dark Mode Overrides for Widgets */
[data-theme="dark"] .widget {
    background: var(--bg-white);
}

[data-theme="dark"] .widget-title {
	color: var(--text-primary);
}

[data-theme="dark"] .widget-recent-posts ul li,
[data-theme="dark"] .widget-archives ul li,
[data-theme="dark"] .widget-category ul li {
    color: var(--text-secondary);
}

[data-theme="dark"] .widget-recent-posts ul li::before,
[data-theme="dark"] .widget-archives ul li::before,
[data-theme="dark"] .widget-category ul li::before {
    color: var(--text-primary);
}

/**
 * 6.0 - Footer
 */

#site-footer {
	padding-top: 10px;
	padding: 0 0 1.5em 0;
	background-color: var(--bg-white, transparent);
	transition: background-color var(--theme-transition, 0.3s ease);
}

.copyright {
	text-align: center;
	padding-top: 1em;
	margin: 0;
	border-top: 1px solid var(--border-light, #eee);
	color: var(--text-secondary, #666);
	transition: color var(--theme-transition, 0.3s ease), border-color var(--theme-transition, 0.3s ease);
}

/* Dark Mode Overrides for Footer */
[data-theme="dark"] #site-footer {
	background-color: var(--bg-white);
}

[data-theme="dark"] .copyright {
	border-top-color: var(--border-light);
	color: var(--text-secondary);
}

/**
 * 7.0 - Header Search Bar
 */

#header-search-box {
	position: absolute;
	right: 38px;
	top: 8px;
}

.search-form {
	display: none;
	width: 25%;
	position: absolute;
	min-width: 200px;
	right: -6px;
	top: 33px;
	background: var(--bg-white, #fff);
	border: 1px solid var(--border-light, #ddd);
	transition: background-color var(--theme-transition, 0.3s ease), border-color var(--theme-transition, 0.3s ease);
}

#search-menu span {
	font-size: 20px;
	color: var(--text-primary, #333);
	transition: color var(--theme-transition, 0.3s ease);
}

#searchform {
    position: relative;
	border: 1px solid var(--border-light, #ddd);
	min-height: 42px;
	background: var(--bg-white, #fff);
	transition: background-color var(--theme-transition, 0.3s ease), border-color var(--theme-transition, 0.3s ease);
}

#searchform input[type=search] {
	width: 100%;
	border: none;
	position: absolute;
	left: 0;
	padding: 10px 30px 10px 10px;
	z-index: 99;
	background: var(--bg-white, #fff);
	color: var(--text-primary, #333);
	transition: background-color var(--theme-transition, 0.3s ease), color var(--theme-transition, 0.3s ease);
}

#searchform input[type=search]::placeholder {
	color: var(--text-muted, #999);
	transition: color var(--theme-transition, 0.3s ease);
}

#searchform button {
	position: absolute;
	right: 6px;
	top: 4px;
	z-index: 999;
	background: transparent;
	border: 0;
	padding: 0;
	color: var(--text-primary, #333);
	transition: color var(--theme-transition, 0.3s ease);
}

/* Dark Mode Overrides for Header Search Bar */
[data-theme="dark"] .search-form {
	background: var(--bg-white);
	border-color: var(--border-light);
}

[data-theme="dark"] #search-menu span {
	color: var(--text-primary);
}

[data-theme="dark"] #searchform {
	border-color: var(--border-light);
	background: var(--bg-white);
}

[data-theme="dark"] #searchform input[type=search] {
	background: var(--bg-white);
	color: var(--text-primary);
}

[data-theme="dark"] #searchform input[type=search]::placeholder {
	color: var(--text-muted);
}

[data-theme="dark"] #searchform button {
	color: var(--text-primary);
}

#searchform button span {
	font-size: 22px;
	color: var(--text-primary, #333);
	transition: color var(--theme-transition, 0.3s ease);
}

#search-menu span.ion-ios-close-empty {
    font-size: 40px;
    line-height: 0;
    position: relative;
    top: -6px;
    color: var(--text-primary, #333);
    transition: color var(--theme-transition, 0.3s ease);
}

/**
 * 8.0 - Mobile Menu
 */

.overlay {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background: var(--bg-white, #fff);
	transition: background-color var(--theme-transition, 0.3s ease);
}

.overlay .overlay-close {
	position: absolute;
	right: 25px;
	top: 10px;
	padding: 0;
	overflow: hidden;
	border: none;
	color: transparent;
	background-color: transparent;
	z-index: 100;
}

.overlay-hugeinc.open .ion-ios-close-empty {
    color: var(--text-primary, #000);
    font-size: 50px;
    transition: color var(--theme-transition, 0.3s ease);
}

.overlay nav {
	text-align: center;
	position: relative;
	top: 50%;
	height: 60%;
	font-size: 54px;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
}

.overlay ul {
	list-style: none;
	padding: 0;
	margin: 0 auto;
	display: inline-block;
	height: 100%;
	position: relative;
}

.overlay ul li {
	display: block;
	height: 20%;
	height: calc(100% / 5);
	min-height: 54px;
}

.overlay ul li a {
	font-weight: 300;
	display: block;
	color: var(--text-primary, #333);
	-webkit-transition: color 0.2s, color var(--theme-transition, 0.3s ease);
	transition: color 0.2s, color var(--theme-transition, 0.3s ease);
}

.overlay ul li a:hover,
.overlay ul li a:focus {
	color: var(--kernel-dark, #000);
}

/* Dark Mode Overrides for Mobile Menu */
[data-theme="dark"] #searchform button span {
	color: var(--text-primary);
}

[data-theme="dark"] #search-menu span.ion-ios-close-empty {
    color: var(--text-primary);
}

[data-theme="dark"] .overlay {
	background: var(--bg-white);
}

[data-theme="dark"] .overlay-hugeinc.open .ion-ios-close-empty {
    color: var(--text-primary);
}

[data-theme="dark"] .overlay ul li a {
	color: var(--text-primary);
}

[data-theme="dark"] .overlay ul li a:hover,
[data-theme="dark"] .overlay ul li a:focus {
	color: var(--kernel-dark);
}

.overlay-hugeinc {
	opacity: 0;
	visibility: hidden;
	-webkit-transition: opacity 0.5s, visibility 0s 0.5s;
	transition: opacity 0.5s, visibility 0s 0.5s;
}

.overlay-hugeinc.open {
	opacity: 1;
	visibility: visible;
	-webkit-transition: opacity 0.5s;
	transition: opacity 0.5s;
}

.overlay-hugeinc nav {
	-webkit-perspective: 1200px;
	perspective: 1200px;
}

.overlay-hugeinc nav ul {
	opacity: 0.4;
	-webkit-transform: translateY(-25%) rotateX(35deg);
	transform: translateY(-25%) rotateX(35deg);
	-webkit-transition: -webkit-transform 0.5s, opacity 0.5s;
	transition: transform 0.5s, opacity 0.5s;
}

.overlay-hugeinc.open nav ul {
	opacity: 1;
	-webkit-transform: rotateX(0deg);
	transform: rotateX(0deg);
}

.overlay-hugeinc.close nav ul {
	-webkit-transform: translateY(25%) rotateX(-35deg);
	transform: translateY(25%) rotateX(-35deg);
}

/**
 * 9.0 - Contact Page Social
 */

.social {
    list-style-type: none;
    padding: 0;
    margin: 0;
    text-align: center;
}

.social li {
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 20px;
}

.social li a {
	border: 1px solid var(--text-secondary, #888);
	font-size: 22px;
	color: var(--text-secondary, #888);
	transition: all 0.3s ease-in, border-color var(--theme-transition, 0.3s ease), color var(--theme-transition, 0.3s ease), background-color var(--theme-transition, 0.3s ease);
}

.social li a:hover {
	background-color: var(--text-primary, #333);
	color: var(--bg-white, #fff);
	border-color: var(--text-primary, #333);
}

.facebook a {
	padding: 12px 21px;
}

.twitter a {
    padding: 12px 15px;
}

.google-plus a {
    padding: 12px 15px;
}

.tumblr a {
	padding: 12px 20px;
}

/* Dark Mode Overrides for Contact Page Social */
[data-theme="dark"] .social li a {
	border-color: var(--text-secondary);
	color: var(--text-secondary);
}

[data-theme="dark"] .social li a:hover {
	background-color: var(--text-primary);
	color: var(--bg-white);
	border-color: var(--text-primary);
}

/**
 * 10.0 - Contact Form
 */

.contact-form input {
	border: 1px solid var(--border-light, #aaa);
	margin-bottom: 15px;
	width: 100%;
	padding: 15px 15px;
	font-size: 16px;
	line-height: 100%;
	background: var(--bg-white, #fff);
	color: var(--text-primary, #333);
	transition: 0.4s border-color linear, background-color var(--theme-transition, 0.3s ease), color var(--theme-transition, 0.3s ease);
}

.contact-form input::placeholder {
	color: var(--text-muted, #999);
	transition: color var(--theme-transition, 0.3s ease);
}

.contact-form textarea {
	border: 1px solid var(--border-light, #aaa);
	margin-bottom: 15px;
	width: 100%;
	padding: 15px 15px;
	font-size: 16px;
	line-height: 20px !important;
	min-height: 183px;
	background: var(--bg-white, #fff);
	color: var(--text-primary, #333);
	transition: 0.4s border-color linear, background-color var(--theme-transition, 0.3s ease), color var(--theme-transition, 0.3s ease);
}

.contact-form textarea::placeholder {
	color: var(--text-muted, #999);
	transition: color var(--theme-transition, 0.3s ease);
}

.contact-form input:focus,
.contact-form textarea:focus {
	border-color: var(--kernel-dark, #666);
	outline: none;
}

.btn-send {
	background: var(--bg-white, none);
	border: 1px solid var(--border-light, #aaa);
	cursor: pointer;
	padding: 25px 80px;
	display: inline-block;
	letter-spacing: 1px;
	position: relative;
	color: var(--text-secondary, #666);
	transition: all 0.3s, background-color var(--theme-transition, 0.3s ease), color var(--theme-transition, 0.3s ease), border-color var(--theme-transition, 0.3s ease);
}

.btn-5 {
	color: var(--text-secondary, #666);
	height: 70px;
	min-width: 260px;
	line-height: 15px;
	font-size: 16px;
	overflow: hidden;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	backface-visibility: hidden;
	transition: color var(--theme-transition, 0.3s ease);
}

/* Dark Mode Overrides for Contact Form */
[data-theme="dark"] .contact-form input,
[data-theme="dark"] .contact-form textarea {
	border-color: var(--border-light);
	background: var(--bg-white);
	color: var(--text-primary);
}

[data-theme="dark"] .contact-form input::placeholder,
[data-theme="dark"] .contact-form textarea::placeholder {
	color: var(--text-muted);
}

[data-theme="dark"] .contact-form input:focus,
[data-theme="dark"] .contact-form textarea:focus {
	border-color: var(--kernel-dark);
}

[data-theme="dark"] .btn-send {
	background: var(--bg-white);
	border-color: var(--border-light);
	color: var(--text-secondary);
}

[data-theme="dark"] .btn-5 {
	color: var(--text-secondary);
}

.btn-5 span {
	display: inline-block;
	width: 100%;
	height: 100%;
	-webkit-transition: all 0.3s;
	-webkit-backface-visibility: hidden;
	-moz-transition: all 0.3s;
	-moz-backface-visibility: hidden;
	transition: all 0.3s;
	backface-visibility: hidden;
}

.btn-5:before {
	position: absolute;
	height: 100%;
	width: 100%;
	line-height: 2.5;
	font-size: 180%;
	-webkit-transition: all 0.3s;
	-moz-transition: all 0.3s;
	transition: all 0.3s;
}

.btn-5:active:before {
	color: #703b87;
}

.btn-5b:hover span {
	-webkit-transform: translateX(200%);
	-moz-transform: translateX(200%);
	-ms-transform: translateX(200%);
	transform: translateX(200%);
}

.btn-5b:before {
	left: -100%;
	top: 0;
}

.btn-5b:hover:before {
	left: 0;
}

/**
 * 11.0 - Media Query
 */

@media (max-width: 991px) {
	.main-nav a {
		margin-right: 20px;
	}

	#header-search-box {
		position: absolute;
		right: 20px;
	}
}

@media (max-width: 767px) {
	#header-search-box {
		right: 20px;
		top: 9px;
	}

	.main-nav {
		margin-top: 2px;
	}

	.btn-5 span {
    	display: none;
	}

	.btn-5b:before {
	    left: 0;
	}
}

@media (max-width: 431px) {
	.logo h1 {
		margin-top: 8px;
		font-size: 24px;
	}

	.post {
		background: var(--bg-white, #fff);
		padding: 0 10px 0;
	}

	/* Dark mode mobile overrides */
	[data-theme="dark"] .post {
		background: var(--bg-white);
	}

	.more-link {
		font-size: 0.9em;
		line-height: 100%;
	}
}

@media screen and (max-height: 30.5em) {
	.overlay nav {
		height: 70%;
		font-size: 34px;
	}
	.overlay ul li {
		min-height: 34px;
	}
}

/* About Page Styles */
.about-section {
    margin-bottom: 40px;
    padding: 20px 0;
}

.about-section h2 {
    color: var(--text-primary, #333);
    margin-bottom: 20px;
    font-size: 1.8em;
    font-weight: 600;
    transition: color var(--theme-transition, 0.3s ease);
}

.education-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

.education-item h3 {
    color: var(--text-primary, #333);
    margin-bottom: 15px;
    font-size: 1.3em;
    transition: color var(--theme-transition, 0.3s ease);
}

.education-item ul {
    list-style: none;
    padding: 0;
}

.education-item li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.education-item li:before {
    content: "▸";
    position: absolute;
    left: 0;
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.attributes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-top: 20px;
}

.attribute-item h4 {
    color: var(--text-primary, #333);
    margin-bottom: 10px;
    font-size: 1.2em;
    transition: color var(--theme-transition, 0.3s ease);
}

.attribute-item p {
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.experience-item {
    margin-bottom: 30px;
    padding: 20px;
    border-left: 3px solid var(--text-secondary, #666);
    background: var(--cloud-light, #f9f9f9);
    transition: background-color var(--theme-transition, 0.3s ease), border-color var(--theme-transition, 0.3s ease);
}

.experience-item h3 {
    color: var(--text-primary, #333);
    margin-bottom: 15px;
    font-size: 1.3em;
    transition: color var(--theme-transition, 0.3s ease);
}

.experience-item ul {
    list-style: none;
    padding: 0;
}

.experience-item li {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.experience-item li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.contact-info {
    background: var(--cloud-light, #f9f9f9);
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
    transition: background-color var(--theme-transition, 0.3s ease);
}

/* Dark Mode Overrides for About Page */
[data-theme="dark"] .about-section h2 {
    color: var(--text-primary);
}

[data-theme="dark"] .education-item h3 {
    color: var(--text-primary);
}

[data-theme="dark"] .education-item li {
    color: var(--text-secondary);
}

[data-theme="dark"] .education-item li:before {
    color: var(--text-secondary);
}

[data-theme="dark"] .attribute-item h4 {
    color: var(--text-primary);
}

[data-theme="dark"] .attribute-item p {
    color: var(--text-secondary);
}

[data-theme="dark"] .experience-item {
    border-left-color: var(--text-secondary);
    background: var(--cloud-light);
}

[data-theme="dark"] .experience-item h3 {
    color: var(--text-primary);
}

[data-theme="dark"] .experience-item li {
    color: var(--text-secondary);
}

[data-theme="dark"] .experience-item li:before {
    color: var(--text-secondary);
}

[data-theme="dark"] .contact-info {
    background: var(--cloud-light);
}

/* Additional Dark Mode Overrides for Page Content */
[data-theme="dark"] .text-center {
	color: var(--text-primary);
}

[data-theme="dark"] article {
	color: var(--text-secondary);
}

[data-theme="dark"] main {
	color: var(--text-secondary);
}

[data-theme="dark"] section {
	color: var(--text-secondary);
}

[data-theme="dark"] div {
	color: inherit;
}

/**
 * COMPREHENSIVE DARK MODE CSS TEMPLATE
 * Hard-coded styles for 100% text visibility
 * WCAG 2.1 AA Compliant with explicit !important declarations
 */

/* === UNIVERSAL DARK MODE BASE === */
[data-theme="dark"] * {
	border-color: #6b7280 !important;
	transition: all 0.3s ease !important;
}

[data-theme="dark"] body {
	background-color: #111827 !important;
	color: #f9fafb !important;
}

/* === ALL HEADING ELEMENTS === */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6,
[data-theme="dark"] .h1,
[data-theme="dark"] .h2,
[data-theme="dark"] .h3,
[data-theme="dark"] .h4,
[data-theme="dark"] .h5,
[data-theme="dark"] .h6,
[data-theme="dark"] .page-title,
[data-theme="dark"] .entry-title,
[data-theme="dark"] .title,
[data-theme="dark"] .section-title,
[data-theme="dark"] .widget-title,
[data-theme="dark"] .project-title,
[data-theme="dark"] .hero-title,
[data-theme="dark"] .hero-subtitle {
	color: #f9fafb !important;
	background-color: transparent !important;
}

/* === ALL PARAGRAPH AND TEXT ELEMENTS === */
[data-theme="dark"] p,
[data-theme="dark"] span,
[data-theme="dark"] div,
[data-theme="dark"] article,
[data-theme="dark"] section,
[data-theme="dark"] main,
[data-theme="dark"] .entry-content,
[data-theme="dark"] .post,
[data-theme="dark"] .content-body,
[data-theme="dark"] .project-description,
[data-theme="dark"] .hero-description,
[data-theme="dark"] .specialization-description {
	color: #e5e7eb !important;
	background-color: transparent !important;
}

/* === ALL LIST ELEMENTS === */
[data-theme="dark"] ul,
[data-theme="dark"] ol,
[data-theme="dark"] li,
[data-theme="dark"] dl,
[data-theme="dark"] dt,
[data-theme="dark"] dd {
	color: #e5e7eb !important;
	background-color: transparent !important;
}

/* === EMPHASIS AND STRONG TEXT === */
[data-theme="dark"] strong,
[data-theme="dark"] b,
[data-theme="dark"] .font-weight-bold,
[data-theme="dark"] .fw-bold {
	color: #f9fafb !important;
	background-color: transparent !important;
}

[data-theme="dark"] em,
[data-theme="dark"] i,
[data-theme="dark"] .font-italic,
[data-theme="dark"] .fst-italic {
	color: #d1d5db !important;
	background-color: transparent !important;
}

/* === ALL LINK ELEMENTS === */
[data-theme="dark"] a,
[data-theme="dark"] a:link,
[data-theme="dark"] a:visited {
	color: #60a5fa !important;
	background-color: transparent !important;
}

[data-theme="dark"] a:hover,
[data-theme="dark"] a:focus,
[data-theme="dark"] a:active {
	color: #fb923c !important;
	background-color: transparent !important;
}

/* === HEADER AND LOGO ELEMENTS === */
[data-theme="dark"] #site-header,
[data-theme="dark"] .site-header {
	background-color: #111827 !important;
	border-bottom-color: #6b7280 !important;
}

[data-theme="dark"] .logo,
[data-theme="dark"] .logo h1,
[data-theme="dark"] .logo h1 a,
[data-theme="dark"] .logo h1 a b,
[data-theme="dark"] .logo h1 a strong {
	color: #f9fafb !important;
	background-color: transparent !important;
}

[data-theme="dark"] .logo h1 a:hover,
[data-theme="dark"] .logo h1 a:focus {
	color: #60a5fa !important;
	background-color: transparent !important;
}

/* === NAVIGATION ELEMENTS === */
[data-theme="dark"] .main-nav,
[data-theme="dark"] .main-nav a,
[data-theme="dark"] .nav,
[data-theme="dark"] .navbar-nav,
[data-theme="dark"] .navbar-nav li,
[data-theme="dark"] .navbar-nav li a {
	color: #f9fafb !important;
	background-color: transparent !important;
}

[data-theme="dark"] .main-nav a:hover,
[data-theme="dark"] .main-nav a:focus,
[data-theme="dark"] .navbar-nav li a:hover,
[data-theme="dark"] .navbar-nav li a:focus {
	color: #60a5fa !important;
	background-color: transparent !important;
}

/* === CONTENT CONTAINERS === */
[data-theme="dark"] .container,
[data-theme="dark"] .row,
[data-theme="dark"] .col-md-12,
[data-theme="dark"] .col-md-8,
[data-theme="dark"] .col-md-6,
[data-theme="dark"] .col-md-4,
[data-theme="dark"] .col-sm-12,
[data-theme="dark"] .col-sm-8,
[data-theme="dark"] .col-sm-7,
[data-theme="dark"] .col-sm-6,
[data-theme="dark"] .col-sm-5,
[data-theme="dark"] .col-sm-4,
[data-theme="dark"] .col-xs-12,
[data-theme="dark"] .col-xs-8,
[data-theme="dark"] .col-xs-6,
[data-theme="dark"] .col-xs-4 {
	color: #e5e7eb !important;
	background-color: transparent !important;
}

/* === POST AND ARTICLE ELEMENTS === */
[data-theme="dark"] .post,
[data-theme="dark"] .entry-content,
[data-theme="dark"] .entry-content p,
[data-theme="dark"] .entry-content div,
[data-theme="dark"] .entry-content section,
[data-theme="dark"] .entry-content article {
	color: #e5e7eb !important;
	background-color: #111827 !important;
}

[data-theme="dark"] .entry-content h1,
[data-theme="dark"] .entry-content h2,
[data-theme="dark"] .entry-content h3,
[data-theme="dark"] .entry-content h4,
[data-theme="dark"] .entry-content h5,
[data-theme="dark"] .entry-content h6 {
	color: #f9fafb !important;
	background-color: transparent !important;
}

[data-theme="dark"] .entry-content strong,
[data-theme="dark"] .entry-content b {
	color: #f9fafb !important;
	background-color: transparent !important;
}

[data-theme="dark"] .entry-content ul,
[data-theme="dark"] .entry-content ol,
[data-theme="dark"] .entry-content li {
	color: #e5e7eb !important;
	background-color: transparent !important;
}

/* === ABOUT PAGE SPECIFIC ELEMENTS === */
[data-theme="dark"] .about-section,
[data-theme="dark"] .about-section h2,
[data-theme="dark"] .about-section h3,
[data-theme="dark"] .about-section h4,
[data-theme="dark"] .about-section p {
	color: #e5e7eb !important;
	background-color: transparent !important;
}

[data-theme="dark"] .about-section h2,
[data-theme="dark"] .about-section h3,
[data-theme="dark"] .about-section h4 {
	color: #f9fafb !important;
}

[data-theme="dark"] .education-grid,
[data-theme="dark"] .education-item,
[data-theme="dark"] .education-item h3,
[data-theme="dark"] .education-item ul,
[data-theme="dark"] .education-item li {
	color: #e5e7eb !important;
	background-color: transparent !important;
}

[data-theme="dark"] .education-item h3 {
	color: #f9fafb !important;
}

[data-theme="dark"] .experience-item,
[data-theme="dark"] .experience-item h3,
[data-theme="dark"] .experience-item p,
[data-theme="dark"] .experience-item ul,
[data-theme="dark"] .experience-item li {
	color: #e5e7eb !important;
	background-color: #374151 !important;
	border-left-color: #6b7280 !important;
}

[data-theme="dark"] .experience-item h3 {
	color: #f9fafb !important;
}

[data-theme="dark"] .attributes-grid,
[data-theme="dark"] .attribute-item,
[data-theme="dark"] .attribute-item h4,
[data-theme="dark"] .attribute-item p {
	color: #e5e7eb !important;
	background-color: transparent !important;
}

[data-theme="dark"] .attribute-item h4 {
	color: #f9fafb !important;
}

/* === PROJECT PAGE SPECIFIC ELEMENTS === */
[data-theme="dark"] .project-card,
[data-theme="dark"] .project-item,
[data-theme="dark"] .project-title,
[data-theme="dark"] .project-description,
[data-theme="dark"] .project-links {
	color: #e5e7eb !important;
	background-color: #1f2937 !important;
}

[data-theme="dark"] .project-title,
[data-theme="dark"] .project-card h3,
[data-theme="dark"] .project-card h4 {
	color: #f9fafb !important;
}

/* === CONTACT PAGE SPECIFIC ELEMENTS === */
[data-theme="dark"] .contact-info,
[data-theme="dark"] .contact-info-section,
[data-theme="dark"] .contact-details,
[data-theme="dark"] .contact-item,
[data-theme="dark"] .contact-item h3,
[data-theme="dark"] .contact-item p,
[data-theme="dark"] .contact-item ul,
[data-theme="dark"] .contact-item li {
	color: #e5e7eb !important;
	background-color: #374151 !important;
}

[data-theme="dark"] .contact-item h3 {
	color: #f9fafb !important;
}

/* === SUPPORT PAGE SPECIFIC ELEMENTS === */
[data-theme="dark"] .support-section,
[data-theme="dark"] .support-project,
[data-theme="dark"] .support-option,
[data-theme="dark"] .feature-item {
	color: #e5e7eb !important;
	background-color: #374151 !important;
}

[data-theme="dark"] .support-project h3,
[data-theme="dark"] .support-option h3,
[data-theme="dark"] .feature-item h4 {
	color: #f9fafb !important;
}

/* === FORM ELEMENTS === */
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select,
[data-theme="dark"] button,
[data-theme="dark"] .btn,
[data-theme="dark"] .btn-send,
[data-theme="dark"] .btn-support,
[data-theme="dark"] .contact-form input,
[data-theme="dark"] .contact-form textarea {
	color: #f9fafb !important;
	background-color: #1f2937 !important;
	border-color: #6b7280 !important;
}

[data-theme="dark"] input::placeholder,
[data-theme="dark"] textarea::placeholder {
	color: #d1d5db !important;
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
	border-color: #60a5fa !important;
	box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2) !important;
}

/* === BADGES AND TAGS === */
[data-theme="dark"] .badge,
[data-theme="dark"] .tech-badges .badge,
[data-theme="dark"] .support-tag,
[data-theme="dark"] .badge-tech {
	color: #111827 !important;
	background-color: #e5e7eb !important;
}

[data-theme="dark"] .badge.linux,
[data-theme="dark"] .badge-tech.linux {
	color: #f9fafb !important;
	background-color: #60a5fa !important;
}

[data-theme="dark"] .badge.rust,
[data-theme="dark"] .badge-tech.rust {
	color: #f9fafb !important;
	background-color: #fb923c !important;
}

[data-theme="dark"] .badge.cloud,
[data-theme="dark"] .badge-tech.cloud {
	color: #f9fafb !important;
	background-color: #6b7280 !important;
}

/* === FOOTER ELEMENTS === */
[data-theme="dark"] #site-footer,
[data-theme="dark"] .site-footer,
[data-theme="dark"] .copyright,
[data-theme="dark"] .footer {
	color: #d1d5db !important;
	background-color: #111827 !important;
	border-top-color: #6b7280 !important;
}

/* === WIDGET ELEMENTS === */
[data-theme="dark"] .widget,
[data-theme="dark"] .widget-title,
[data-theme="dark"] .widget-recent-posts,
[data-theme="dark"] .widget-archives,
[data-theme="dark"] .widget-category {
	color: #e5e7eb !important;
	background-color: #111827 !important;
}

[data-theme="dark"] .widget-title {
	color: #f9fafb !important;
}

[data-theme="dark"] .widget ul,
[data-theme="dark"] .widget li {
	color: #e5e7eb !important;
}

/* === MOBILE MENU === */
[data-theme="dark"] .overlay,
[data-theme="dark"] .overlay nav,
[data-theme="dark"] .overlay ul,
[data-theme="dark"] .overlay li,
[data-theme="dark"] .overlay a {
	color: #f9fafb !important;
	background-color: #111827 !important;
}

[data-theme="dark"] .overlay a:hover,
[data-theme="dark"] .overlay a:focus {
	color: #60a5fa !important;
}

/* Support Page Styles */
.support-section {
    margin-bottom: 40px;
    padding: 20px 0;
}

.project-support-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
    margin-top: 20px;
}

.support-project {
    background: var(--cloud-light, #f9f9f9);
    padding: 25px;
    border-radius: 8px;
    border-left: 4px solid var(--text-secondary, #666);
    transition: background-color var(--theme-transition, 0.3s ease), border-color var(--theme-transition, 0.3s ease);
}

.support-project h3 {
    color: var(--text-primary, #333);
    margin-bottom: 15px;
    font-size: 1.3em;
    transition: color var(--theme-transition, 0.3s ease);
}

.support-project p {
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.support-needs {
    margin-top: 15px;
}

.support-tag {
    display: inline-block;
    background: var(--text-secondary, #666);
    color: var(--bg-white, white);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.85em;
    margin-right: 8px;
    margin-bottom: 5px;
    transition: background-color var(--theme-transition, 0.3s ease), color var(--theme-transition, 0.3s ease);
}

.support-options {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
    margin-top: 20px;
}

.support-option {
    background: var(--cloud-light, #f9f9f9);
    padding: 25px;
    border-radius: 8px;
    transition: background-color var(--theme-transition, 0.3s ease);
}

.support-option h3 {
    color: var(--text-primary, #333);
    margin-bottom: 15px;
    font-size: 1.3em;
    transition: color var(--theme-transition, 0.3s ease);
}

.support-option p {
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.btn-support {
    display: inline-block;
    background: var(--text-secondary, #666);
    color: var(--bg-white, white);
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    margin-top: 10px;
    margin-right: 10px;
    transition: background 0.3s ease, background-color var(--theme-transition, 0.3s ease), color var(--theme-transition, 0.3s ease);
}

.btn-support:hover {
    background: var(--text-primary, #333);
    color: var(--bg-white, white);
    text-decoration: none;
}

.btn-support.primary {
    background: var(--text-primary, #000);
}

.j3scandjove-features {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.feature-item {
    text-align: center;
    padding: 20px;
    background: var(--cloud-light, #f9f9f9);
    border-radius: 8px;
    transition: background-color var(--theme-transition, 0.3s ease);
}

.feature-item h4 {
    color: var(--text-primary, #333);
    margin-bottom: 10px;
    font-size: 1.1em;
    transition: color var(--theme-transition, 0.3s ease);
}

.feature-item p {
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.contact-methods {
    margin-top: 15px;
}

.contact-method {
    margin-bottom: 10px;
    padding: 10px;
    background: var(--cloud-light, #f9f9f9);
    border-radius: 5px;
    transition: background-color var(--theme-transition, 0.3s ease);
}

/* Dark Mode Overrides for Support Page */
[data-theme="dark"] .support-project {
    background: var(--cloud-light);
    border-left-color: var(--text-secondary);
}

[data-theme="dark"] .support-project h3 {
    color: var(--text-primary);
}

[data-theme="dark"] .support-project p {
    color: var(--text-secondary);
}

[data-theme="dark"] .support-tag {
    background: var(--text-secondary);
    color: var(--bg-white);
}

[data-theme="dark"] .support-option {
    background: var(--cloud-light);
}

[data-theme="dark"] .support-option h3 {
    color: var(--text-primary);
}

[data-theme="dark"] .support-option p {
    color: var(--text-secondary);
}

[data-theme="dark"] .btn-support {
    background: var(--text-secondary);
    color: var(--bg-white);
}

[data-theme="dark"] .btn-support:hover {
    background: var(--text-primary);
    color: var(--bg-white);
}

[data-theme="dark"] .btn-support.primary {
    background: var(--text-primary);
}

[data-theme="dark"] .feature-item {
    background: var(--cloud-light);
}

[data-theme="dark"] .feature-item h4 {
    color: var(--text-primary);
}

[data-theme="dark"] .feature-item p {
    color: var(--text-secondary);
}

[data-theme="dark"] .contact-method {
    background: var(--cloud-light);
}

/* Contact Page Styles */
.contact-info-section {
    margin-bottom: 40px;
}

.contact-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
    margin-top: 20px;
}

.contact-item {
    background: var(--cloud-light, #f9f9f9);
    padding: 20px;
    border-radius: 8px;
    transition: background-color var(--theme-transition, 0.3s ease);
}

.contact-item h3 {
    color: var(--text-primary, #333);
    margin-bottom: 15px;
    font-size: 1.3em;
    transition: color var(--theme-transition, 0.3s ease);
}

.contact-item p {
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.contact-item ul {
    list-style: none;
    padding: 0;
}

.contact-item li {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

.contact-item li:before {
    content: "▸";
    position: absolute;
    left: 0;
    color: var(--text-secondary, #666);
    transition: color var(--theme-transition, 0.3s ease);
}

/* Dark Mode Overrides for Contact Page */
[data-theme="dark"] .contact-item {
    background: var(--cloud-light);
}

[data-theme="dark"] .contact-item h3 {
    color: var(--text-primary);
}

[data-theme="dark"] .contact-item p {
    color: var(--text-secondary);
}

[data-theme="dark"] .contact-item li {
    color: var(--text-secondary);
}

[data-theme="dark"] .contact-item li:before {
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .education-grid,
    .attributes-grid,
    .j3scandjove-features {
        grid-template-columns: 1fr;
    }

    .project-support-grid,
    .support-options,
    .contact-details {
        grid-template-columns: 1fr;
    }
}

/**
 * COMPREHENSIVE HARD-CODED DARK MODE SOLUTION
 * Ensures 100% text visibility across all pages
 * WCAG 2.1 AA Compliant with explicit !important declarations
 * Professional Linux Kernel Engineer branding maintained
 */

/* === HARD-CODED DARK MODE BASE STYLES === */
[data-theme="dark"] {
  /* Override all CSS variables with hard-coded values */
  --text-primary: #f9fafb !important;        /* 15.8:1 contrast ratio */
  --text-secondary: #e5e7eb !important;      /* 9.5:1 contrast ratio */
  --text-muted: #d1d5db !important;          /* 6.2:1 contrast ratio */
  --text-inverse: #111827 !important;        /* Dark text for light backgrounds */
  --bg-primary: #111827 !important;          /* Dark background */
  --bg-secondary: #1f2937 !important;        /* Secondary dark background */
  --bg-tertiary: #374151 !important;         /* Tertiary dark background */
  --bg-white: #111827 !important;            /* Dark background for white elements */
  --border-light: #6b7280 !important;        /* Border color */
  --kernel-dark: #60a5fa !important;         /* Link color - 7.2:1 contrast */
  --rust-accent: #fb923c !important;         /* Hover color - 5.8:1 contrast */
  --cloud-light: #374151 !important;         /* Cloud/infrastructure color */
}

/* === UNIVERSAL DARK MODE BODY === */
[data-theme="dark"] body {
  background-color: #111827 !important;
  color: #f9fafb !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED HEADING STYLES === */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6,
[data-theme="dark"] .h1,
[data-theme="dark"] .h2,
[data-theme="dark"] .h3,
[data-theme="dark"] .h4,
[data-theme="dark"] .h5,
[data-theme="dark"] .h6,
[data-theme="dark"] .page-title,
[data-theme="dark"] .entry-title,
[data-theme="dark"] .title,
[data-theme="dark"] .section-title,
[data-theme="dark"] .widget-title,
[data-theme="dark"] .project-title,
[data-theme="dark"] .hero-title,
[data-theme="dark"] .hero-subtitle,
[data-theme="dark"] .cta-title {
  color: #f9fafb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED TEXT ELEMENT STYLES === */
[data-theme="dark"] p,
[data-theme="dark"] span,
[data-theme="dark"] div,
[data-theme="dark"] article,
[data-theme="dark"] section,
[data-theme="dark"] main,
[data-theme="dark"] .entry-content,
[data-theme="dark"] .post,
[data-theme="dark"] .content-body,
[data-theme="dark"] .project-description,
[data-theme="dark"] .hero-description,
[data-theme="dark"] .specialization-description,
[data-theme="dark"] .cta-description {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED LIST ELEMENT STYLES === */
[data-theme="dark"] ul,
[data-theme="dark"] ol,
[data-theme="dark"] li,
[data-theme="dark"] dl,
[data-theme="dark"] dt,
[data-theme="dark"] dd {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED EMPHASIS STYLES === */
[data-theme="dark"] strong,
[data-theme="dark"] b,
[data-theme="dark"] .font-weight-bold,
[data-theme="dark"] .fw-bold {
  color: #f9fafb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] em,
[data-theme="dark"] i,
[data-theme="dark"] .font-italic,
[data-theme="dark"] .fst-italic {
  color: #d1d5db !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED LINK STYLES === */
[data-theme="dark"] a,
[data-theme="dark"] a:link,
[data-theme="dark"] a:visited {
  color: #60a5fa !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] a:hover,
[data-theme="dark"] a:focus,
[data-theme="dark"] a:active {
  color: #fb923c !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED HEADER AND LOGO STYLES === */
[data-theme="dark"] #site-header,
[data-theme="dark"] .site-header {
  background-color: #111827 !important;
  border-bottom-color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .logo,
[data-theme="dark"] .logo h1,
[data-theme="dark"] .logo h1 a,
[data-theme="dark"] .logo h1 a b,
[data-theme="dark"] .logo h1 a strong {
  color: #f9fafb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .logo h1 a:hover,
[data-theme="dark"] .logo h1 a:focus {
  color: #60a5fa !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED NAVIGATION STYLES === */
[data-theme="dark"] .main-nav,
[data-theme="dark"] .main-nav a,
[data-theme="dark"] .nav,
[data-theme="dark"] .navbar-nav,
[data-theme="dark"] .navbar-nav li,
[data-theme="dark"] .navbar-nav li a {
  color: #f9fafb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .main-nav a:hover,
[data-theme="dark"] .main-nav a:focus,
[data-theme="dark"] .navbar-nav li a:hover,
[data-theme="dark"] .navbar-nav li a:focus {
  color: #60a5fa !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .navbar-toggle span {
  color: #f9fafb !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED CONTAINER STYLES === */
[data-theme="dark"] .container,
[data-theme="dark"] .row,
[data-theme="dark"] .col-md-12,
[data-theme="dark"] .col-md-8,
[data-theme="dark"] .col-md-6,
[data-theme="dark"] .col-md-4,
[data-theme="dark"] .col-sm-12,
[data-theme="dark"] .col-sm-8,
[data-theme="dark"] .col-sm-7,
[data-theme="dark"] .col-sm-6,
[data-theme="dark"] .col-sm-5,
[data-theme="dark"] .col-sm-4,
[data-theme="dark"] .col-xs-12,
[data-theme="dark"] .col-xs-8,
[data-theme="dark"] .col-xs-6,
[data-theme="dark"] .col-xs-4 {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED POST AND ARTICLE STYLES === */
[data-theme="dark"] .post,
[data-theme="dark"] .entry-content,
[data-theme="dark"] .entry-content p,
[data-theme="dark"] .entry-content div,
[data-theme="dark"] .entry-content section,
[data-theme="dark"] .entry-content article {
  color: #e5e7eb !important;
  background-color: #111827 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .entry-content h1,
[data-theme="dark"] .entry-content h2,
[data-theme="dark"] .entry-content h3,
[data-theme="dark"] .entry-content h4,
[data-theme="dark"] .entry-content h5,
[data-theme="dark"] .entry-content h6 {
  color: #f9fafb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .entry-content strong,
[data-theme="dark"] .entry-content b {
  color: #f9fafb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .entry-content ul,
[data-theme="dark"] .entry-content ol,
[data-theme="dark"] .entry-content li {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED ABOUT PAGE STYLES === */
[data-theme="dark"] .about-section,
[data-theme="dark"] .about-section h2,
[data-theme="dark"] .about-section h3,
[data-theme="dark"] .about-section h4,
[data-theme="dark"] .about-section p {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .about-section h2,
[data-theme="dark"] .about-section h3,
[data-theme="dark"] .about-section h4 {
  color: #f9fafb !important;
}

[data-theme="dark"] .education-grid,
[data-theme="dark"] .education-item,
[data-theme="dark"] .education-item h3,
[data-theme="dark"] .education-item ul,
[data-theme="dark"] .education-item li {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .education-item h3 {
  color: #f9fafb !important;
}

[data-theme="dark"] .experience-item,
[data-theme="dark"] .experience-item h3,
[data-theme="dark"] .experience-item p,
[data-theme="dark"] .experience-item ul,
[data-theme="dark"] .experience-item li {
  color: #e5e7eb !important;
  background-color: #374151 !important;
  border-left-color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .experience-item h3 {
  color: #f9fafb !important;
}

[data-theme="dark"] .attributes-grid,
[data-theme="dark"] .attribute-item,
[data-theme="dark"] .attribute-item h4,
[data-theme="dark"] .attribute-item p {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .attribute-item h4 {
  color: #f9fafb !important;
}

/* === HARD-CODED PROJECT PAGE STYLES === */
[data-theme="dark"] .project-card,
[data-theme="dark"] .project-item,
[data-theme="dark"] .project-title,
[data-theme="dark"] .project-description,
[data-theme="dark"] .project-links {
  color: #e5e7eb !important;
  background-color: #1f2937 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .project-title,
[data-theme="dark"] .project-card h3,
[data-theme="dark"] .project-card h4 {
  color: #f9fafb !important;
}

[data-theme="dark"] .projects-grid,
[data-theme="dark"] .featured-projects {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED CONTACT PAGE STYLES === */
[data-theme="dark"] .contact-info,
[data-theme="dark"] .contact-info-section,
[data-theme="dark"] .contact-details,
[data-theme="dark"] .contact-item,
[data-theme="dark"] .contact-item h3,
[data-theme="dark"] .contact-item p,
[data-theme="dark"] .contact-item ul,
[data-theme="dark"] .contact-item li {
  color: #e5e7eb !important;
  background-color: #374151 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .contact-item h3 {
  color: #f9fafb !important;
}

/* === HARD-CODED SUPPORT PAGE STYLES === */
[data-theme="dark"] .support-section,
[data-theme="dark"] .support-project,
[data-theme="dark"] .support-option,
[data-theme="dark"] .feature-item {
  color: #e5e7eb !important;
  background-color: #374151 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .support-project h3,
[data-theme="dark"] .support-option h3,
[data-theme="dark"] .feature-item h4 {
  color: #f9fafb !important;
}

/* === HARD-CODED FORM ELEMENT STYLES === */
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select,
[data-theme="dark"] button,
[data-theme="dark"] .btn,
[data-theme="dark"] .btn-send,
[data-theme="dark"] .btn-support,
[data-theme="dark"] .contact-form input,
[data-theme="dark"] .contact-form textarea {
  color: #f9fafb !important;
  background-color: #1f2937 !important;
  border-color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] input::placeholder,
[data-theme="dark"] textarea::placeholder {
  color: #d1d5db !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2) !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED BADGE AND TAG STYLES === */
[data-theme="dark"] .badge,
[data-theme="dark"] .tech-badges .badge,
[data-theme="dark"] .support-tag,
[data-theme="dark"] .badge-tech {
  color: #111827 !important;
  background-color: #e5e7eb !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .badge.linux,
[data-theme="dark"] .badge-tech.linux {
  color: #f9fafb !important;
  background-color: #60a5fa !important;
}

[data-theme="dark"] .badge.rust,
[data-theme="dark"] .badge-tech.rust {
  color: #f9fafb !important;
  background-color: #fb923c !important;
}

[data-theme="dark"] .badge.cloud,
[data-theme="dark"] .badge-tech.cloud {
  color: #f9fafb !important;
  background-color: #6b7280 !important;
}

/* === HARD-CODED FOOTER STYLES === */
[data-theme="dark"] #site-footer,
[data-theme="dark"] .site-footer,
[data-theme="dark"] .copyright,
[data-theme="dark"] .footer {
  color: #d1d5db !important;
  background-color: #111827 !important;
  border-top-color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED WIDGET STYLES === */
[data-theme="dark"] .widget,
[data-theme="dark"] .widget-title,
[data-theme="dark"] .widget-recent-posts,
[data-theme="dark"] .widget-archives,
[data-theme="dark"] .widget-category {
  color: #e5e7eb !important;
  background-color: #111827 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .widget-title {
  color: #f9fafb !important;
}

[data-theme="dark"] .widget ul,
[data-theme="dark"] .widget li {
  color: #e5e7eb !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED MOBILE MENU STYLES === */
[data-theme="dark"] .overlay,
[data-theme="dark"] .overlay nav,
[data-theme="dark"] .overlay ul,
[data-theme="dark"] .overlay li,
[data-theme="dark"] .overlay a {
  color: #f9fafb !important;
  background-color: #111827 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .overlay a:hover,
[data-theme="dark"] .overlay a:focus {
  color: #60a5fa !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .overlay-hugeinc.open .ion-ios-close-empty {
  color: #f9fafb !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED HERO SECTION WITH BACKGROUND LOGO === */
[data-theme="dark"] .hero-section {
  border-bottom-color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .hero-with-logo-bg {
  background-image: url('../img/dark-logo.jpeg') !important;
  background-size: cover !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  background-attachment: fixed !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .hero-background-overlay {
  background: linear-gradient(135deg,
    rgba(17, 24, 39, 0.85) 0%,
    rgba(55, 65, 81, 0.90) 100%) !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .hero-title {
  color: #f9fafb !important;
  background-color: transparent !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .hero-subtitle {
  color: #60a5fa !important;
  background-color: transparent !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .hero-description {
  color: #e5e7eb !important;
  background-color: transparent !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  transition: all 0.3s ease !important;
}

/* Light mode explicit styles for background logo */
[data-theme="light"] .hero-with-logo-bg,
html:not([data-theme="dark"]) .hero-with-logo-bg {
  background-image: url('../img/light-logo.jpeg') !important;
  background-size: cover !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  background-attachment: fixed !important;
  transition: all 0.3s ease !important;
}

[data-theme="light"] .hero-background-overlay,
html:not([data-theme="dark"]) .hero-background-overlay {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.85) 0%,
    rgba(248, 250, 252, 0.90) 100%) !important;
  transition: all 0.3s ease !important;
}

/* Enhanced text readability with background logos */
[data-theme="light"] .hero-title,
html:not([data-theme="dark"]) .hero-title {
  color: #111827 !important;
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease !important;
}

[data-theme="light"] .hero-subtitle,
html:not([data-theme="dark"]) .hero-subtitle {
  color: #1f2937 !important;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease !important;
}

[data-theme="light"] .hero-description,
html:not([data-theme="dark"]) .hero-description {
  color: #374151 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .hero-actions {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED SKILLS SECTION STYLES === */
[data-theme="dark"] .skills-matrix,
[data-theme="dark"] .skills-grid,
[data-theme="dark"] .skill-category,
[data-theme="dark"] .skill-item {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .skill-category h3 {
  color: #f9fafb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .skill-name,
[data-theme="dark"] .skill-name span {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .skills-scroller,
[data-theme="dark"] .skills-scroll-content {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED CTA SECTION STYLES === */
[data-theme="dark"] .cta-section {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .cta-title {
  color: #f9fafb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .cta-description {
  color: #e5e7eb !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED BUTTON STYLES === */
[data-theme="dark"] .btn-primary,
[data-theme="dark"] .btn-secondary,
[data-theme="dark"] .btn-cta,
[data-theme="dark"] .btn-project {
  color: #f9fafb !important;
  background-color: #1f2937 !important;
  border-color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .btn-primary:hover,
[data-theme="dark"] .btn-secondary:hover,
[data-theme="dark"] .btn-cta:hover,
[data-theme="dark"] .btn-project:hover {
  color: #f9fafb !important;
  background-color: #60a5fa !important;
  border-color: #60a5fa !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .btn-project.primary {
  color: #f9fafb !important;
  background-color: #60a5fa !important;
  border-color: #60a5fa !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .btn-project.primary:hover {
  color: #f9fafb !important;
  background-color: #fb923c !important;
  border-color: #fb923c !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED SEARCH AND UTILITY STYLES === */
[data-theme="dark"] .search-form,
[data-theme="dark"] #searchform,
[data-theme="dark"] #searchform input[type=search] {
  color: #f9fafb !important;
  background-color: #1f2937 !important;
  border-color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] #search-menu span,
[data-theme="dark"] #searchform button,
[data-theme="dark"] #searchform button span {
  color: #f9fafb !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED TEXT UTILITY CLASSES === */
[data-theme="dark"] .text-center,
[data-theme="dark"] .text-left,
[data-theme="dark"] .text-right {
  color: #e5e7eb !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .text-muted {
  color: #d1d5db !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .text-primary {
  color: #f9fafb !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .text-secondary {
  color: #e5e7eb !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED SOCIAL MEDIA STYLES === */
[data-theme="dark"] .social,
[data-theme="dark"] .social li,
[data-theme="dark"] .social li a {
  color: #e5e7eb !important;
  border-color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .social li a:hover {
  color: #f9fafb !important;
  background-color: #60a5fa !important;
  border-color: #60a5fa !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED SPECIAL ELEMENT STYLES === */
[data-theme="dark"] .entry-meta,
[data-theme="dark"] .post-category,
[data-theme="dark"] .post-date,
[data-theme="dark"] .post-author {
  color: #d1d5db !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .post-category::after,
[data-theme="dark"] .post-date::after,
[data-theme="dark"] .post-author::after {
  color: #f9fafb !important;
  transition: all 0.3s ease !important;
}

/* === HARD-CODED SKILL BAR STYLES === */
[data-theme="dark"] .skill-bar {
  background-color: #374151 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .skill-progress {
  transition: all 0.3s ease !important;
}

/* === HARD-CODED THEME TOGGLE STYLES === */
[data-theme="dark"] .theme-toggle,
[data-theme="dark"] #theme-toggle {
  color: #f9fafb !important;
  background-color: transparent !important;
  border-color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .theme-toggle:hover,
[data-theme="dark"] #theme-toggle:hover {
  color: #60a5fa !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* === COMPREHENSIVE FALLBACK STYLES === */
[data-theme="dark"] * {
  border-color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

/* Ensure all text is visible with fallback */
[data-theme="dark"] *:not(input):not(textarea):not(select):not(button) {
  color: #e5e7eb !important;
}

/* Override for headings and important text */
[data-theme="dark"] h1 *,
[data-theme="dark"] h2 *,
[data-theme="dark"] h3 *,
[data-theme="dark"] h4 *,
[data-theme="dark"] h5 *,
[data-theme="dark"] h6 *,
[data-theme="dark"] .title *,
[data-theme="dark"] .hero-title *,
[data-theme="dark"] .section-title * {
  color: #f9fafb !important;
}

/* === END HARD-CODED DARK MODE SOLUTION === */

/**
 * HERO SECTION POSITIONING FIXES
 * Comprehensive fixes for hero section positioning issues across all pages
 * Maintains professional branding and dark mode compatibility
 */

/* === STATIC HERO SECTION - NO ANIMATIONS OR MOVEMENT === */
.hero-section {
  position: relative !important;
  z-index: 1 !important;
  transform: none !important; /* Completely disable all transforms */
  will-change: auto !important; /* No animation optimization needed */
  backface-visibility: hidden !important; /* Prevent rendering issues */
  -webkit-backface-visibility: hidden !important;
  margin-bottom: 0 !important; /* Reset margin for consistent spacing */
  padding: 80px 0 !important; /* Consistent padding */
  overflow: hidden !important; /* Prevent content overflow */
  animation: none !important; /* Disable all animations */
  transition: background-color 0.3s ease, border-color 0.3s ease !important; /* Only theme transitions */
}

/* Force static positioning class */
.hero-section.static-hero {
  transform: none !important;
  animation: none !important;
  will-change: auto !important;
}

/* Disable any parallax or movement effects */
.hero-section.parallax-disabled,
.hero-section.static-hero {
  transform: none !important;
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -ms-transform: none !important;
  -o-transform: none !important;
}

/* Prevent parallax transform conflicts */
.hero-section.parallax-disabled {
  transform: none !important;
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -ms-transform: none !important;
}

/* === HERO SECTION CONTAINER FIXES === */
.hero-section .container {
  position: relative !important;
  z-index: 2 !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 0 15px !important;
}

.hero-section .row {
  margin: 0 !important;
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  justify-content: center !important;
}

.hero-section .col-md-12 {
  padding: 0 15px !important;
  text-align: center !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* === STATIC HERO CONTENT - NO ANIMATIONS OR MOVEMENT === */
.hero-title {
  position: relative !important;
  z-index: 3 !important;
  margin: 0 0 20px 0 !important;
  padding: 0 !important;
  line-height: 1.2 !important;
  font-weight: 300 !important;
  letter-spacing: -0.02em !important;
  text-rendering: optimizeLegibility !important;
  transform: none !important; /* No transforms */
  animation: none !important; /* No animations */
  transition: color 0.3s ease !important; /* Only theme color transitions */
  will-change: auto !important; /* No animation optimization */
}

.hero-subtitle {
  position: relative !important;
  z-index: 3 !important;
  margin: 0 0 25px 0 !important;
  padding: 0 !important;
  line-height: 1.4 !important;
  font-weight: 400 !important;
  transform: none !important; /* No transforms */
  animation: none !important; /* No animations */
  transition: color 0.3s ease !important; /* Only theme color transitions */
  will-change: auto !important; /* No animation optimization */
}

.hero-description {
  position: relative !important;
  z-index: 3 !important;
  margin: 0 auto 40px auto !important;
  padding: 0 20px !important;
  max-width: 800px !important;
  line-height: 1.6 !important;
  transform: none !important; /* No transforms */
  animation: none !important; /* No animations */
  transition: color 0.3s ease !important; /* Only theme color transitions */
  will-change: auto !important; /* No animation optimization */
}

.hero-actions {
  position: relative !important;
  z-index: 3 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 20px !important;
  flex-wrap: wrap !important;
  margin: 40px 0 0 0 !important;
  padding: 0 !important;
  transform: none !important; /* No transforms */
  animation: none !important; /* No animations */
  transition: none !important; /* No transitions for actions container */
  will-change: auto !important; /* No animation optimization */
}

/* Ensure hero action buttons remain static */
.hero-actions .btn-primary,
.hero-actions .btn-secondary {
  transform: none !important;
  animation: none !important;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
  will-change: auto !important;
}

/* === STATIC SKILLS SCROLLER - NO INTERFERENCE WITH HERO === */
.skills-scroller {
  position: relative !important;
  z-index: 2 !important;
  margin: 30px 0 !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  border-radius: 4px !important;
  contain: layout style !important; /* Prevent layout interference */
}

.skills-scroll-content {
  position: relative !important;
  display: inline-block !important;
  padding: 15px 0 !important;
  animation: scroll-left 30s linear infinite !important;
  will-change: transform !important; /* Only the scroller animates, not hero */
}

/* Ensure skills scroller doesn't affect hero section positioning */
.hero-section .skills-scroller {
  transform: none !important; /* No transforms on hero's scroller */
  position: relative !important;
  z-index: 2 !important;
}

.hero-section .skills-scroll-content {
  animation: scroll-left 30s linear infinite !important; /* Keep animation but isolated */
  transform: none !important; /* No additional transforms */
}

/* === RESPONSIVE HERO POSITIONING === */
@media (max-width: 1200px) {
  .hero-section {
    padding: 70px 0 !important;
  }

  .hero-description {
    max-width: 700px !important;
    padding: 0 30px !important;
  }
}

@media (max-width: 992px) {
  .hero-section {
    padding: 60px 0 !important;
  }

  .hero-title {
    font-size: 3rem !important;
    margin-bottom: 15px !important;
  }

  .hero-subtitle {
    font-size: 1.6rem !important;
    margin-bottom: 20px !important;
  }

  .hero-description {
    font-size: 1.1rem !important;
    max-width: 600px !important;
    padding: 0 25px !important;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 50px 0 !important;
  }

  .hero-title {
    font-size: 2.5rem !important;
    margin-bottom: 12px !important;
  }

  .hero-subtitle {
    font-size: 1.4rem !important;
    margin-bottom: 18px !important;
  }

  .hero-description {
    font-size: 1rem !important;
    padding: 0 20px !important;
    margin-bottom: 30px !important;
  }

  .hero-actions {
    flex-direction: column !important;
    gap: 15px !important;
    margin-top: 30px !important;
  }

  .skills-scroller {
    margin: 25px 0 !important;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 40px 0 !important;
  }

  .hero-title {
    font-size: 2rem !important;
    margin-bottom: 10px !important;
  }

  .hero-subtitle {
    font-size: 1.2rem !important;
    margin-bottom: 15px !important;
  }

  .hero-description {
    font-size: 0.95rem !important;
    padding: 0 15px !important;
    margin-bottom: 25px !important;
  }

  .hero-actions {
    margin-top: 25px !important;
  }
}

/* === STATIC DARK MODE HERO WITH BACKGROUND LOGO - NO ANIMATIONS === */
[data-theme="dark"] .hero-section {
  border-bottom: 2px solid #6b7280 !important;
  position: relative !important;
  z-index: 1 !important;
  transform: none !important;
  animation: none !important;
  will-change: auto !important;
  transition: background-image 0.3s ease, border-color 0.3s ease !important;
}

[data-theme="dark"] .hero-with-logo-bg {
  background-image: url('../img/dark-logo.jpeg') !important;
  background-size: cover !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  background-attachment: scroll !important;
  transform: none !important;
  animation: none !important;
  will-change: auto !important;
  transition: background-image 0.3s ease !important;
}

[data-theme="dark"] .hero-background-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 1 !important;
  transform: none !important;
  animation: none !important;
  will-change: auto !important;
  transition: background 0.3s ease !important;
}

[data-theme="dark"] .hero-title {
  color: #f9fafb !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  position: relative !important;
  z-index: 3 !important;
  transform: none !important;
  animation: none !important;
  transition: color 0.3s ease !important;
  will-change: auto !important;
}

[data-theme="dark"] .hero-subtitle {
  color: #60a5fa !important;
  text-shadow: none !important;
  position: relative !important;
  z-index: 3 !important;
  transform: none !important;
  animation: none !important;
  transition: color 0.3s ease !important;
  will-change: auto !important;
}

[data-theme="dark"] .hero-description {
  color: #e5e7eb !important;
  text-shadow: none !important;
  position: relative !important;
  z-index: 3 !important;
  transform: none !important;
  animation: none !important;
  transition: color 0.3s ease !important;
  will-change: auto !important;
}

[data-theme="dark"] .skills-scroller {
  background: #60a5fa !important;
  color: #ffffff !important;
  border: 1px solid #6b7280 !important;
}

/* === COMPREHENSIVE STATIC HERO ENFORCEMENT === */
/* Prevent ANY JavaScript interference with hero section */

.hero-section,
.hero-section *,
.hero-section .hero-title,
.hero-section .hero-subtitle,
.hero-section .hero-description,
.hero-section .hero-actions,
.hero-section .skills-scroller {
  /* Force static positioning - override any JavaScript changes */
  transform: none !important;
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -ms-transform: none !important;
  -o-transform: none !important;

  /* Disable all animations */
  animation: none !important;
  -webkit-animation: none !important;
  -moz-animation: none !important;
  -ms-animation: none !important;
  -o-animation: none !important;

  /* Reset will-change to prevent optimization conflicts */
  will-change: auto !important;
  -webkit-will-change: auto !important;

  /* Only allow theme-related transitions */
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease !important;
  -webkit-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease !important;
  -moz-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease !important;
  -ms-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease !important;
  -o-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease !important;
}

/* Exception: Allow skills scroller content to animate (but not affect hero positioning) */
.hero-section .skills-scroll-content {
  animation: scroll-left 30s linear infinite !important;
  transform: none !important; /* But no transforms */
  will-change: transform !important; /* Only for the scrolling animation */
}

/* Prevent any external JavaScript from modifying hero section styles */
.hero-section[style*="transform"],
.hero-section[style*="animation"],
.hero-section[style*="will-change"] {
  transform: none !important;
  animation: none !important;
  will-change: auto !important;
}

/* Force static positioning on all hero child elements */
.hero-section > *,
.hero-section .container > *,
.hero-section .row > *,
.hero-section .col-md-12 > * {
  position: relative !important;
  transform: none !important;
  animation: none !important;
  will-change: auto !important;
}

/* Ensure hero section remains static during scroll events */
.hero-section.scrolling,
.hero-section.parallax-active,
.hero-section.animated {
  transform: none !important;
  animation: none !important;
  will-change: auto !important;
}

/* === CROSS-PAGE LAYOUT CONSISTENCY === */
/* Ensure consistent spacing and positioning across all pages */

.content-body {
  position: relative !important;
  z-index: 1 !important;
  margin-top: 0 !important;
  padding-top: 0 !important;
  min-height: calc(100vh - 200px) !important;
}

/* Page title positioning for non-hero pages */
.page-title {
  position: relative !important;
  z-index: 2 !important;
  margin: 40px 0 30px 0 !important;
  padding: 0 !important;
  text-align: center !important;
  font-size: 2.5rem !important;
  font-weight: 300 !important;
  line-height: 1.2 !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .page-title {
  color: #f9fafb !important;
}

/* === HEADER POSITIONING FIXES === */
#site-header {
  position: relative !important;
  z-index: 100 !important;
  background-color: var(--bg-primary) !important;
  border-bottom: 1px solid var(--border-light) !important;
  margin-bottom: 0 !important;
  padding: 20px 0 !important;
}

[data-theme="dark"] #site-header {
  background-color: #111827 !important;
  border-bottom-color: #6b7280 !important;
}

/* Logo positioning */
.logo {
  position: relative !important;
  z-index: 101 !important;
}

.logo h1 {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1 !important;
}

/* Navigation positioning */
.main-nav {
  position: relative !important;
  z-index: 101 !important;
}

.navbar-nav {
  margin: 0 !important;
  padding: 0 !important;
}

/* Theme toggle positioning */
#header-theme-toggle {
  position: relative !important;
  z-index: 102 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end !important;
}

.theme-toggle {
  position: relative !important;
  z-index: 103 !important;
}

/* === FOOTER POSITIONING === */
#site-footer {
  position: relative !important;
  z-index: 10 !important;
  margin-top: auto !important;
  background-color: var(--bg-primary) !important;
  border-top: 1px solid var(--border-light) !important;
  padding: 30px 0 !important;
}

[data-theme="dark"] #site-footer {
  background-color: #111827 !important;
  border-top-color: #6b7280 !important;
}

/* === MOBILE MENU POSITIONING === */
.overlay {
  position: fixed !important;
  z-index: 9999 !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

.overlay nav {
  position: relative !important;
  z-index: 10000 !important;
}

/* === SMOOTH TRANSITIONS FOR POSITIONING === */
.hero-section,
.content-body,
#site-header,
#site-footer,
.page-title {
  transition: all 0.3s ease !important;
}

/* === LAYOUT SHIFT PREVENTION === */
/* Prevent cumulative layout shift (CLS) issues */

.hero-section,
.content-body {
  contain: layout style !important;
}

/* Preload space for hero section */
.hero-section::before {
  content: '' !important;
  display: block !important;
  height: 0 !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* === BROWSER-SPECIFIC FIXES === */
/* Chrome/Safari positioning fixes */
@supports (-webkit-appearance: none) {
  .hero-section {
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
  }
}

/* Firefox positioning fixes */
@-moz-document url-prefix() {
  .hero-section {
    -moz-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
  }
}

/* Edge/IE positioning fixes */
@supports (-ms-ime-align: auto) {
  .hero-section {
    -ms-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
  }
}

/* === ACCESSIBILITY POSITIONING === */
/* Ensure proper focus management and screen reader navigation */
.hero-section:focus-within {
  outline: none !important;
}

.hero-title:focus,
.hero-subtitle:focus,
.hero-description:focus {
  outline: 2px solid var(--kernel-dark) !important;
  outline-offset: 2px !important;
}

[data-theme="dark"] .hero-title:focus,
[data-theme="dark"] .hero-subtitle:focus,
[data-theme="dark"] .hero-description:focus {
  outline-color: #60a5fa !important;
}

/* === PRINT STYLES === */
@media print {
  .hero-section {
    position: static !important;
    transform: none !important;
    page-break-inside: avoid !important;
    background: white !important;
    color: black !important;
    border: none !important;
    margin: 0 !important;
    padding: 20px 0 !important;
  }

  .skills-scroller {
    display: none !important;
  }

  .hero-actions {
    display: none !important;
  }
}