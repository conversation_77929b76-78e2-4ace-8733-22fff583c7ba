<!DOCTYPE html>
<html lang="en">
<head>
    <title>Contrast & Accessibility Test - <PERSON></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSS in the same order as other pages -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/ionicons.min.css">
    <link rel="stylesheet" href="css/pace.css">
    <link rel="stylesheet" href="css/design-system.css">
    <link rel="stylesheet" href="css/custom.css">
    <link rel="stylesheet" href="css/portfolio.css">

    <!-- Early theme application to prevent FOUC -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('portfolio-theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
        })();
    </script>
    
    <!-- JS -->
    <script src="js/jquery-2.1.3.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/pace.min.js"></script>
    <script src="js/modernizr.custom.js"></script>

    <style>
        .contrast-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid var(--border-light);
            border-radius: 8px;
        }
        .contrast-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .color-sample {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid var(--border-light);
        }
        .contrast-info {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }
        .test-button {
            margin: 5px;
            padding: 10px 20px;
            border: 1px solid var(--border-light);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
    </style>
</head>

<body>
    <div class="container">
        <header id="site-header" role="banner">
            <div class="row">
                <div class="col-md-4 col-sm-5 col-xs-8">
                    <div class="logo">
                        <h1><a href="index.html"><b>Daniel</b> Orji</a></h1>
                    </div>
                </div>
                <div class="col-md-8 col-sm-7 col-xs-4">
                    <nav class="main-nav" role="navigation">
                        <div class="navbar-header">
                            <button type="button" id="trigger-overlay" class="navbar-toggle">
                                <span class="ion-navicon"></span>
                            </button>
                        </div>
                        <div class="collapse navbar-collapse">
                            <ul class="nav navbar-nav navbar-right">
                                <li class="cl-effect-11"><a href="index.html" data-hover="Home">Home</a></li>
                                <li class="cl-effect-11"><a href="projects.html" data-hover="Projects">Projects</a></li>
                                <li class="cl-effect-11"><a href="about.html" data-hover="About">About</a></li>
                                <li class="cl-effect-11"><a href="contact.html" data-hover="Contact">Contact</a></li>
                                <li class="cl-effect-11"><a href="support.html" data-hover="Support">Support</a></li>
                            </ul>
                        </div>
                    </nav>
                    <div id="header-theme-toggle">
                        <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode" title="Toggle dark/light theme">
                            <svg class="theme-toggle-icon sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            </svg>
                            <svg class="theme-toggle-icon moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </header>
    </div>

    <div class="content-body">
        <div class="container">
            <div class="row">
                <main class="col-md-12">
                    <h1 class="page-title">Contrast & Accessibility Test</h1>
                    
                    <div class="post">
                        <div id="theme-status" style="padding: 15px; margin: 20px 0; border: 2px solid var(--kernel-dark); border-radius: 8px; text-align: center; font-weight: bold;">
                            Current Theme: <span id="current-theme">Loading...</span>
                        </div>

                        <!-- Typography Test -->
                        <section class="contrast-section">
                            <h2>Typography Contrast Test</h2>
                            <div class="contrast-grid">
                                <div>
                                    <h3>Headings</h3>
                                    <h1>H1 Heading - Primary Text</h1>
                                    <h2>H2 Heading - Primary Text</h2>
                                    <h3>H3 Heading - Primary Text</h3>
                                    <h4>H4 Heading - Primary Text</h4>
                                </div>
                                <div>
                                    <h3>Body Text</h3>
                                    <p>Regular paragraph text using secondary color for readability testing. This should maintain at least 4.5:1 contrast ratio.</p>
                                    <p><strong>Bold text</strong> and <em>italic text</em> variations.</p>
                                    <p><a href="#">Link text with hover effects</a> for navigation testing.</p>
                                </div>
                            </div>
                        </section>

                        <!-- Interactive Elements Test -->
                        <section class="contrast-section">
                            <h2>Interactive Elements Test</h2>
                            <div class="contrast-grid">
                                <div>
                                    <h3>Buttons</h3>
                                    <button class="btn-send btn-5 btn-5b test-button">Primary Button</button>
                                    <button class="test-button" style="background: var(--kernel-dark); color: var(--bg-white);">Kernel Button</button>
                                    <button class="test-button" style="background: var(--rust-accent); color: var(--bg-white);">Rust Button</button>
                                </div>
                                <div>
                                    <h3>Form Elements</h3>
                                    <input type="text" placeholder="Test input field" style="width: 100%; margin: 5px 0; padding: 10px;">
                                    <textarea placeholder="Test textarea" style="width: 100%; margin: 5px 0; padding: 10px; height: 80px;"></textarea>
                                </div>
                            </div>
                        </section>

                        <!-- Badge and Tag Test -->
                        <section class="contrast-section">
                            <h2>Badges and Tags Test</h2>
                            <div class="tech-badges">
                                <span class="badge linux">Linux Badge</span>
                                <span class="badge rust">Rust Badge</span>
                                <span class="badge cloud">Cloud Badge</span>
                                <span class="badge">Default Badge</span>
                            </div>
                            <div style="margin-top: 15px;">
                                <span class="support-tag">Support Tag 1</span>
                                <span class="support-tag">Support Tag 2</span>
                                <span class="support-tag">Support Tag 3</span>
                            </div>
                        </section>

                        <!-- Project Card Test -->
                        <section class="contrast-section">
                            <h2>Project Card Test</h2>
                            <div class="project-card">
                                <h3 class="project-title">Test Project Card</h3>
                                <p class="project-description">This project card tests the contrast ratios for card backgrounds, text, and interactive elements in both light and dark modes.</p>
                                <div class="tech-badges">
                                    <span class="badge linux">C Programming</span>
                                    <span class="badge rust">Rust</span>
                                    <span class="badge cloud">Linux Kernel</span>
                                </div>
                                <div class="project-links" style="margin-top: 15px;">
                                    <a href="#" class="btn-project primary">View Details</a>
                                    <a href="#" class="btn-project">Source Code</a>
                                </div>
                            </div>
                        </section>

                        <!-- Color Variables Display -->
                        <section class="contrast-section">
                            <h2>Color Variables Test</h2>
                            <div class="contrast-grid">
                                <div>
                                    <div class="color-sample" style="background: var(--text-primary); color: var(--bg-white);">
                                        Primary Text on White Background
                                        <div class="contrast-info">var(--text-primary) on var(--bg-white)</div>
                                    </div>
                                    <div class="color-sample" style="background: var(--text-secondary); color: var(--bg-white);">
                                        Secondary Text on White Background
                                        <div class="contrast-info">var(--text-secondary) on var(--bg-white)</div>
                                    </div>
                                    <div class="color-sample" style="background: var(--kernel-dark); color: var(--bg-white);">
                                        Kernel Dark on White Background
                                        <div class="contrast-info">var(--kernel-dark) on var(--bg-white)</div>
                                    </div>
                                </div>
                                <div>
                                    <div class="color-sample" style="background: var(--bg-white); color: var(--text-primary); border: 2px solid var(--border-light);">
                                        Primary Text on Page Background
                                        <div class="contrast-info">var(--text-primary) on var(--bg-white)</div>
                                    </div>
                                    <div class="color-sample" style="background: var(--cloud-light); color: var(--text-primary);">
                                        Primary Text on Cloud Background
                                        <div class="contrast-info">var(--text-primary) on var(--cloud-light)</div>
                                    </div>
                                    <div class="color-sample" style="background: var(--rust-accent); color: var(--bg-white);">
                                        White Text on Rust Background
                                        <div class="contrast-info">var(--bg-white) on var(--rust-accent)</div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <footer id="site-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <p class="copyright">&copy; 2024 Daniel Orji - Contrast Test</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/portfolio.js"></script>
    
    <script>
        function updateThemeStatus() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            document.getElementById('current-theme').textContent = currentTheme.toUpperCase();
        }
        
        document.addEventListener('DOMContentLoaded', updateThemeStatus);
        document.addEventListener('themeChanged', updateThemeStatus);
        setInterval(updateThemeStatus, 1000);
    </script>
</body>
</html>
