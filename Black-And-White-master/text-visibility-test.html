<!DOCTYPE html>
<html lang="en">
<head>
    <title>Text Visibility Test - <PERSON></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSS in the same order as other pages -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/ionicons.min.css">
    <link rel="stylesheet" href="css/pace.css">
    <link rel="stylesheet" href="css/design-system.css">
    <link rel="stylesheet" href="css/custom.css">
    <link rel="stylesheet" href="css/portfolio.css">

    <!-- Early theme application to prevent FOUC -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('portfolio-theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
        })();
    </script>
    
    <!-- JS -->
    <script src="js/jquery-2.1.3.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/pace.min.js"></script>
    <script src="js/modernizr.custom.js"></script>

    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid var(--border-light);
            border-radius: 8px;
        }
        .color-debug {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid var(--border-light);
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="container">
        <header id="site-header" role="banner">
            <div class="row">
                <div class="col-md-4 col-sm-5 col-xs-8">
                    <div class="logo">
                        <h1><a href="index.html"><b>Daniel</b> Orji</a></h1>
                    </div>
                </div>
                <div class="col-md-8 col-sm-7 col-xs-4">
                    <nav class="main-nav" role="navigation">
                        <div class="navbar-header">
                            <button type="button" id="trigger-overlay" class="navbar-toggle">
                                <span class="ion-navicon"></span>
                            </button>
                        </div>
                        <div class="collapse navbar-collapse">
                            <ul class="nav navbar-nav navbar-right">
                                <li class="cl-effect-11"><a href="index.html" data-hover="Home">Home</a></li>
                                <li class="cl-effect-11"><a href="projects.html" data-hover="Projects">Projects</a></li>
                                <li class="cl-effect-11"><a href="about.html" data-hover="About">About</a></li>
                                <li class="cl-effect-11"><a href="contact.html" data-hover="Contact">Contact</a></li>
                                <li class="cl-effect-11"><a href="support.html" data-hover="Support">Support</a></li>
                            </ul>
                        </div>
                    </nav>
                    <div id="header-theme-toggle">
                        <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode" title="Toggle dark/light theme">
                            <svg class="theme-toggle-icon sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            </svg>
                            <svg class="theme-toggle-icon moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </header>
    </div>

    <div class="content-body">
        <div class="container">
            <div class="row">
                <main class="col-md-12">
                    <h1 class="page-title">Text Visibility Diagnostic Test</h1>
                    
                    <div class="post">
                        <div class="entry-content">
                            <!-- Header Test -->
                            <section class="test-section">
                                <h2>Header Logo Test</h2>
                                <p>Check if the header logo "Daniel Orji" is visible in both light and dark modes.</p>
                                <div class="color-debug">
                                    Logo should use: var(--text-primary) in dark mode
                                </div>
                            </section>

                            <!-- Standard HTML Elements Test -->
                            <section class="test-section">
                                <h2>Standard HTML Elements Test</h2>
                                <h1>H1 Heading Test</h1>
                                <h2>H2 Heading Test</h2>
                                <h3>H3 Heading Test</h3>
                                <h4>H4 Heading Test</h4>
                                <p>Regular paragraph text that should be visible in both light and dark modes.</p>
                                <p><strong>Bold text test</strong> and <em>italic text test</em></p>
                                <ul>
                                    <li>List item 1</li>
                                    <li>List item 2 with <strong>bold text</strong></li>
                                    <li>List item 3</li>
                                </ul>
                            </section>

                            <!-- About Page Structure Test -->
                            <section class="test-section about-section">
                                <h2>About Page Structure Test</h2>
                                <p>This mimics the about page structure to test visibility.</p>
                                <div class="education-grid">
                                    <div class="education-item">
                                        <h3>Test Section</h3>
                                        <ul>
                                            <li><strong>Test Item 1</strong> - Description text</li>
                                            <li><strong>Test Item 2</strong> - More description</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="experience-item">
                                    <h3>Experience Test</h3>
                                    <ul>
                                        <li>Experience item 1</li>
                                        <li>Experience item 2</li>
                                    </ul>
                                </div>
                            </section>

                            <!-- Color Variable Display -->
                            <section class="test-section">
                                <h2>Color Variables Test</h2>
                                <div class="color-debug" style="background: var(--bg-white); color: var(--text-primary);">
                                    Primary text on white background: var(--text-primary) on var(--bg-white)
                                </div>
                                <div class="color-debug" style="background: var(--bg-white); color: var(--text-secondary);">
                                    Secondary text on white background: var(--text-secondary) on var(--bg-white)
                                </div>
                                <div class="color-debug" style="background: var(--bg-primary); color: var(--text-primary);">
                                    Primary text on primary background: var(--text-primary) on var(--bg-primary)
                                </div>
                            </section>

                            <!-- Theme Status -->
                            <section class="test-section">
                                <h2>Theme Status</h2>
                                <p id="theme-status">Current theme: <span id="current-theme">Loading...</span></p>
                                <p>Toggle the theme using the button in the header and observe text visibility changes.</p>
                            </section>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <footer id="site-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <p class="copyright">&copy; 2024 Daniel Orji - Text Visibility Test</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/portfolio.js"></script>
    <script src="js/dark-mode-enforcer.js"></script>
    
    <script>
        function updateThemeStatus() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            document.getElementById('current-theme').textContent = currentTheme.toUpperCase();
            
            // Log computed styles for debugging
            const body = document.body;
            const computedStyle = getComputedStyle(body);
            console.log('Current theme:', currentTheme);
            console.log('Body background-color:', computedStyle.backgroundColor);
            console.log('Body color:', computedStyle.color);
            
            // Test specific elements
            const logo = document.querySelector('.logo h1 a');
            if (logo) {
                const logoStyle = getComputedStyle(logo);
                console.log('Logo color:', logoStyle.color);
            }
            
            const pageTitle = document.querySelector('.page-title');
            if (pageTitle) {
                const titleStyle = getComputedStyle(pageTitle);
                console.log('Page title color:', titleStyle.color);
            }
        }
        
        document.addEventListener('DOMContentLoaded', updateThemeStatus);
        document.addEventListener('themeChanged', updateThemeStatus);
        
        // Update every 2 seconds for debugging
        setInterval(updateThemeStatus, 2000);
    </script>
</body>
</html>
