<!DOCTYPE html>
<html lang="en">
<head>
    <title>Logo Implementation Verification | <PERSON></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/ionicons.min.css">
    <link rel="stylesheet" href="css/pace.css">
    <link rel="stylesheet" href="css/design-system.css">
    <link rel="stylesheet" href="css/custom.css">
    <link rel="stylesheet" href="css/portfolio.css">
    
    <!-- Early theme application -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('portfolio-theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
        })();
    </script>
    
    <style>
        .verification-container {
            max-width: 1000px;
            margin: 40px auto;
            padding: 20px;
            background: var(--bg-white);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        [data-theme="dark"] .verification-container {
            background: #1f2937 !important;
            color: #f9fafb !important;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-card {
            padding: 20px;
            border: 1px solid var(--border-light);
            border-radius: 6px;
            background: var(--bg-white);
        }
        
        [data-theme="dark"] .test-card {
            border-color: #6b7280 !important;
            background: #374151 !important;
            color: #f9fafb !important;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-pass { background: #10b981; }
        .status-fail { background: #ef4444; }
        .status-pending { background: #f59e0b; }
        
        .logo-comparison {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed var(--border-light);
            border-radius: 8px;
        }
        
        [data-theme="dark"] .logo-comparison {
            border-color: #6b7280 !important;
        }
        
        .logo-sample {
            text-align: center;
        }
        
        .logo-sample img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .current-logo-display {
            border: 3px solid var(--rust-accent);
        }
        
        .verification-btn {
            background: var(--rust-accent);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .verification-btn:hover {
            background: var(--kernel-dark);
            transform: translateY(-2px);
        }
        
        [data-theme="dark"] .verification-btn {
            background: #60a5fa !important;
            color: #111827 !important;
        }
        
        [data-theme="dark"] .verification-btn:hover {
            background: #fb923c !important;
        }
        
        .summary-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid var(--rust-accent);
        }
        
        [data-theme="dark"] .summary-section {
            background: #1e293b !important;
            color: #f9fafb !important;
        }
    </style>
</head>

<body>
    <div class="container">
        <header id="site-header">
            <div class="row">
                <div class="col-md-4 col-sm-5 col-xs-8">
                    <div class="logo">
                        <h1><a href="index.html"><b>Daniel</b> Orji</a></h1>
                    </div>
                </div>
                <div class="col-md-8 col-sm-7 col-xs-4">
                    <nav class="main-nav">
                        <ul class="nav navbar-nav navbar-right">
                            <li><a href="index.html">Home</a></li>
                            <li><a href="projects.html">Projects</a></li>
                            <li><a href="about.html">About</a></li>
                            <li><a href="contact.html">Contact</a></li>
                            <li><a href="support.html">Support</a></li>
                        </ul>
                    </nav>
                    <div id="header-theme-toggle">
                        <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                            <svg class="theme-toggle-icon sun-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            </svg>
                            <svg class="theme-toggle-icon moon-icon" fill="currentColor" viewBox="0 0 20 20" style="display: none;">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </header>
    </div>

    <!-- Hero Section with Dynamic Background Logo -->
    <section class="hero-section hero-with-logo-bg" role="banner" aria-labelledby="hero-title">
        <div class="hero-background-overlay"></div>
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h1 id="hero-title" class="hero-title">Background Logo Implementation Verification</h1>
                    <h2 class="hero-subtitle">Dynamic Background Logo Switching Test Suite</h2>
                    <p class="hero-description">
                        Comprehensive verification of the dynamic background logo switching implementation.
                        Tests theme-based background logo switching, accessibility compliance, and performance.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Verification Container -->
    <div class="verification-container">
        <h2>Implementation Verification Results</h2>
        
        <div class="text-center">
            <button class="verification-btn" onclick="runFullVerification()">Run Full Verification</button>
            <button class="verification-btn" onclick="toggleThemeAndTest()">Toggle Theme & Test</button>
        </div>
        
        <!-- Logo Comparison -->
        <div class="logo-comparison">
            <div class="logo-sample">
                <img src="img/light-logo.jpeg" alt="Light Logo" id="light-sample">
                <div>Light Logo</div>
            </div>
            <div class="logo-sample">
                <div style="font-size: 24px; margin: 20px 0;">→</div>
                <div>Current Theme: <strong id="current-theme-name">Light</strong></div>
            </div>
            <div class="logo-sample">
                <img src="img/dark-logo.jpeg" alt="Dark Logo" id="dark-sample">
                <div>Dark Logo</div>
            </div>
        </div>
        
        <!-- Test Results Grid -->
        <div class="test-grid">
            <div class="test-card">
                <h3><span class="status-indicator status-pending" id="logo-status"></span>Logo Switching</h3>
                <div id="logo-test-details">Click "Run Full Verification" to test</div>
            </div>
            
            <div class="test-card">
                <h3><span class="status-indicator status-pending" id="accessibility-status"></span>Accessibility</h3>
                <div id="accessibility-test-details">Click "Run Full Verification" to test</div>
            </div>
            
            <div class="test-card">
                <h3><span class="status-indicator status-pending" id="performance-status"></span>Performance</h3>
                <div id="performance-test-details">Click "Run Full Verification" to test</div>
            </div>
            
            <div class="test-card">
                <h3><span class="status-indicator status-pending" id="responsive-status"></span>Responsive Design</h3>
                <div id="responsive-test-details">Click "Run Full Verification" to test</div>
            </div>
            
            <div class="test-card">
                <h3><span class="status-indicator status-pending" id="wcag-status"></span>WCAG Compliance</h3>
                <div id="wcag-test-details">Click "Run Full Verification" to test</div>
            </div>
            
            <div class="test-card">
                <h3><span class="status-indicator status-pending" id="integration-status"></span>Integration</h3>
                <div id="integration-test-details">Click "Run Full Verification" to test</div>
            </div>
        </div>
        
        <!-- Summary Section -->
        <div class="summary-section">
            <h3>Verification Summary</h3>
            <div id="verification-summary">Run verification to see results</div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery-2.1.3.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/script.js"></script>
    <script src="js/portfolio.js"></script>
    <script src="js/dark-mode-enforcer.js"></script>
    
    <script>
        let verificationResults = {
            logoSwitching: false,
            accessibility: false,
            performance: false,
            responsive: false,
            wcag: false,
            integration: false
        };
        
        function updateThemeDisplay() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            document.getElementById('current-theme-name').textContent = 
                currentTheme.charAt(0).toUpperCase() + currentTheme.slice(1);
            
            // Update logo comparison visual indicators
            const lightSample = document.getElementById('light-sample');
            const darkSample = document.getElementById('dark-sample');
            
            lightSample.classList.toggle('current-logo-display', currentTheme === 'light');
            darkSample.classList.toggle('current-logo-display', currentTheme === 'dark');
        }
        
        function updateStatus(testName, passed, details) {
            const statusElement = document.getElementById(`${testName}-status`);
            const detailsElement = document.getElementById(`${testName}-test-details`);
            
            statusElement.className = `status-indicator status-${passed ? 'pass' : 'fail'}`;
            detailsElement.innerHTML = details;
            
            verificationResults[testName] = passed;
        }
        
        function testLogoSwitching() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const heroSection = document.querySelector('.hero-with-logo-bg');

            if (!heroSection) {
                updateStatus('logoSwitching', false, 'Hero section with background logo not found');
                return false;
            }

            const heroStyle = getComputedStyle(heroSection);
            const backgroundImage = heroStyle.backgroundImage;

            let passed = false;
            let details = '';

            if (currentTheme === 'light') {
                passed = backgroundImage.includes('light-logo.jpeg');
                details = passed ?
                    `✅ Light logo background applied correctly` :
                    `❌ Light logo background not applied - Current: ${backgroundImage}`;
            } else {
                passed = backgroundImage.includes('dark-logo.jpeg');
                details = passed ?
                    `✅ Dark logo background applied correctly` :
                    `❌ Dark logo background not applied - Current: ${backgroundImage}`;
            }

            updateStatus('logoSwitching', passed, details);
            return passed;
        }
        
        function testAccessibility() {
            const heroSection = document.querySelector('.hero-with-logo-bg');
            const overlay = document.querySelector('.hero-background-overlay');

            let passed = true;
            let issues = [];

            // Check hero section structure
            if (!heroSection) issues.push('Hero section with background logo not found');
            if (!overlay) issues.push('Background overlay not found');

            // Check ARIA attributes on hero section
            if (heroSection && !heroSection.getAttribute('role')) issues.push('Missing role attribute on hero section');
            if (heroSection && !heroSection.getAttribute('aria-labelledby')) issues.push('Missing aria-labelledby on hero section');

            // Check background image accessibility
            const heroStyle = heroSection ? getComputedStyle(heroSection) : null;
            if (heroStyle && heroStyle.backgroundImage === 'none') {
                issues.push('No background image applied');
            }

            // Check overlay for content readability
            const overlayStyle = overlay ? getComputedStyle(overlay) : null;
            if (overlayStyle && overlayStyle.background === 'none') {
                issues.push('Background overlay not applied for content readability');
            }

            passed = issues.length === 0;
            const details = passed ?
                '✅ All accessibility requirements met (ARIA, background structure, readability)' :
                `❌ Issues found: ${issues.join(', ')}`;

            updateStatus('accessibility', passed, details);
            return passed;
        }
        
        function testPerformance() {
            const startTime = performance.now();
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            // Test switching performance
            document.documentElement.setAttribute('data-theme', newTheme);
            
            setTimeout(() => {
                document.documentElement.setAttribute('data-theme', currentTheme);
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                const passed = duration < 300; // Should complete within 300ms
                const details = passed ? 
                    `✅ Logo switching completed in ${duration.toFixed(2)}ms (< 300ms target)` :
                    `❌ Logo switching too slow: ${duration.toFixed(2)}ms (> 300ms target)`;
                
                updateStatus('performance', passed, details);
                updateThemeDisplay();
            }, 100);
            
            return true;
        }
        
        function testResponsiveDesign() {
            const logoContainer = document.querySelector('.hero-logo-container');
            const containerStyle = getComputedStyle(logoContainer);
            
            // Check if responsive styles are applied
            const hasResponsiveStyles = containerStyle.width && containerStyle.height;
            const hasTransitions = containerStyle.transition.includes('0.3s');
            
            const passed = hasResponsiveStyles && hasTransitions;
            const details = passed ? 
                `✅ Responsive design working (size: ${containerStyle.width} × ${containerStyle.height}, transitions: ${hasTransitions})` :
                `❌ Responsive design issues detected`;
            
            updateStatus('responsive', passed, details);
            return passed;
        }
        
        function testWCAGCompliance() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const heroSection = document.querySelector('.hero-section');
            const logoContainer = document.querySelector('.hero-logo-container');
            
            // Check contrast and visibility
            const heroStyle = getComputedStyle(heroSection);
            const containerStyle = getComputedStyle(logoContainer);
            
            // Check if proper theme-based styling is applied
            let passed = true;
            let details = '';
            
            if (currentTheme === 'dark') {
                const bgColor = heroStyle.backgroundColor;
                passed = bgColor.includes('17, 24, 39') || bgColor.includes('#111827');
                details = passed ? 
                    '✅ WCAG compliant dark mode styling applied' :
                    '❌ Dark mode styling not properly applied';
            } else {
                passed = true; // Light mode is baseline
                details = '✅ WCAG compliant light mode styling';
            }
            
            updateStatus('wcag', passed, details);
            return passed;
        }
        
        function testIntegration() {
            // Test integration with existing dark mode system
            const themeToggleExists = document.getElementById('theme-toggle') !== null;
            const darkModeEnforcerExists = window.darkModeEnforcer !== undefined;
            const portfolioJSExists = window.jQuery !== undefined;
            
            const passed = themeToggleExists && portfolioJSExists;
            const details = passed ? 
                `✅ Integration successful (Theme toggle: ${themeToggleExists}, jQuery: ${portfolioJSExists}, Dark mode enforcer: ${darkModeEnforcerExists})` :
                `❌ Integration issues detected`;
            
            updateStatus('integration', passed, details);
            return passed;
        }
        
        function toggleThemeAndTest() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('portfolio-theme', newTheme);
            
            setTimeout(() => {
                updateThemeDisplay();
                testLogoSwitching();
            }, 300);
        }
        
        function runFullVerification() {
            console.log('🧪 Running Full Logo Implementation Verification...');
            
            setTimeout(() => {
                testLogoSwitching();
                testAccessibility();
                testPerformance();
                testResponsiveDesign();
                testWCAGCompliance();
                testIntegration();
                
                // Generate summary
                const totalTests = Object.keys(verificationResults).length;
                const passedTests = Object.values(verificationResults).filter(result => result).length;
                const passRate = ((passedTests / totalTests) * 100).toFixed(1);
                
                const summaryElement = document.getElementById('verification-summary');
                const summaryClass = passedTests === totalTests ? 'text-success' : 'text-warning';
                
                summaryElement.innerHTML = `
                    <div class="${summaryClass}">
                        <strong>Verification Complete:</strong> ${passedTests}/${totalTests} tests passed (${passRate}%)
                    </div>
                    <div style="margin-top: 10px;">
                        ${passedTests === totalTests ? 
                            '🎉 All tests passed! Dynamic logo implementation is working correctly.' :
                            '⚠️ Some tests failed. Please review the individual test results above.'}
                    </div>
                `;
            }, 100);
        }
        
        // Initialize
        $(document).ready(function() {
            updateThemeDisplay();
            
            // Listen for theme changes
            $(document).on('themeChanged', function() {
                setTimeout(() => {
                    updateThemeDisplay();
                }, 100);
            });
        });
    </script>
</body>
</html>
