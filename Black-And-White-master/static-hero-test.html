<!DOCTYPE html>
<html lang="en">
<head>
    <title>Static Hero Section Test | <PERSON></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/ionicons.min.css">
    <link rel="stylesheet" href="css/pace.css">
    <link rel="stylesheet" href="css/design-system.css">
    <link rel="stylesheet" href="css/custom.css">
    <link rel="stylesheet" href="css/portfolio.css">
    
    <!-- Early theme application -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('portfolio-theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
        })();
    </script>
    
    <style>
        .test-section {
            margin: 30px 0;
            padding: 25px;
            border: 2px solid #60a5fa;
            border-radius: 10px;
            background-color: var(--bg-secondary, #f8f9fa);
        }
        
        [data-theme="dark"] .test-section {
            background-color: #1f2937;
            border-color: #60a5fa;
        }
        
        .test-header {
            color: var(--text-primary);
            font-size: 24px;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--border-light);
            padding-bottom: 10px;
        }
        
        .test-result {
            display: inline-block;
            padding: 8px 15px;
            border-radius: 5px;
            margin: 5px;
            font-weight: bold;
        }
        
        .test-pass { background-color: #10b981; color: #ffffff; }
        .test-fail { background-color: #ef4444; color: #ffffff; }
        .test-warning { background-color: #f59e0b; color: #ffffff; }
        
        .property-display {
            background-color: var(--bg-tertiary, #e9ecef);
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        [data-theme="dark"] .property-display {
            background-color: #374151;
            color: #e5e7eb;
        }
        
        .scroll-test-area {
            height: 300px;
            overflow-y: auto;
            border: 2px solid var(--border-light);
            padding: 20px;
            margin: 20px 0;
        }
        
        .scroll-content {
            height: 1000px;
            background: linear-gradient(to bottom, #60a5fa, #fb923c, #10b981, #ef4444);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }
        
        .interference-test {
            text-align: center;
            margin: 20px 0;
        }
        
        .interference-test button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background-color: #ef4444;
            color: white;
            cursor: pointer;
            font-weight: bold;
        }
        
        .interference-test button:hover {
            background-color: #dc2626;
        }
    </style>
</head>

<body>
    <div class="container">
        <header id="site-header">
            <div class="row">
                <div class="col-md-4 col-sm-5 col-xs-8">
                    <div class="logo">
                        <h1><a href="index.html"><b>Daniel</b> Orji</a></h1>
                    </div>
                </div>
                <div class="col-md-8 col-sm-7 col-xs-4">
                    <nav class="main-nav">
                        <ul class="nav navbar-nav navbar-right">
                            <li><a href="index.html">Home</a></li>
                            <li><a href="projects.html">Projects</a></li>
                            <li><a href="about.html">About</a></li>
                            <li><a href="contact.html">Contact</a></li>
                            <li><a href="support.html">Support</a></li>
                        </ul>
                    </nav>
                    <div id="header-theme-toggle">
                        <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                            <svg class="theme-toggle-icon sun-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            </svg>
                            <svg class="theme-toggle-icon moon-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </header>
    </div>

    <!-- Static Hero Section -->
    <section class="hero-section static-hero" role="banner" aria-labelledby="hero-title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <!-- Dynamic Logo -->
                    <div class="hero-logo-container" role="img" aria-label="Daniel Orji Professional Logo">
                        <img src="img/light-logo.jpeg"
                             alt="Daniel Orji - Linux Kernel Engineer"
                             class="hero-logo light-logo"
                             loading="eager">
                        <img src="img/dark-logo.jpeg"
                             alt="Daniel Orji - Linux Kernel Engineer"
                             class="hero-logo dark-logo"
                             loading="eager">
                    </div>

                    <h1 id="hero-title" class="hero-title">Static Hero Section Test</h1>
                    <h2 class="hero-subtitle">Completely Stationary - No Animations</h2>
                    <p class="hero-description">
                        This hero section is designed to remain completely stationary and unaffected by any JavaScript
                        effects, animations, or user interactions. It maintains professional styling while focusing
                        attention on content rather than dynamic effects.
                    </p>

                    <div class="skills-scroller">
                        <div class="skills-scroll-content">
                            Static Hero • No Parallax • No Animations • Professional Design • Content Focus
                        </div>
                    </div>

                    <div class="hero-actions">
                        <a href="#static-tests" class="btn-primary">Test Static Behavior</a>
                        <a href="#interference-tests" class="btn-secondary">Test Interference Protection</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="content-body">
        <div class="container">
            <div class="row">
                <main id="main-content" class="col-md-12">
                    
                    <!-- Static Behavior Tests -->
                    <section id="static-tests" class="test-section">
                        <h2 class="test-header">🎯 Static Hero Section Properties</h2>
                        
                        <div class="property-display">
                            <strong>Current Hero Section CSS Properties:</strong><br>
                            Position: <span id="hero-position">Loading...</span><br>
                            Transform: <span id="hero-transform">Loading...</span><br>
                            Animation: <span id="hero-animation">Loading...</span><br>
                            Will-Change: <span id="hero-will-change">Loading...</span><br>
                            Z-Index: <span id="hero-zindex">Loading...</span><br>
                            Classes: <span id="hero-classes">Loading...</span>
                        </div>
                        
                        <div id="static-results">
                            <span class="test-result test-warning">🔄 Analyzing properties...</span>
                        </div>
                    </section>

                    <!-- Scroll Test -->
                    <section class="test-section">
                        <h2 class="test-header">📜 Scroll Behavior Test</h2>
                        <p>Scroll in the area below to verify the hero section remains completely stationary:</p>
                        
                        <div class="scroll-test-area">
                            <div class="scroll-content">
                                Scroll Test Content<br>
                                Hero section should NOT move during scrolling<br>
                                No parallax effects should occur<br>
                                Hero remains completely static
                            </div>
                        </div>
                        
                        <div id="scroll-results">
                            <span class="test-result test-warning">🔄 Scroll above to test...</span>
                        </div>
                    </section>

                    <!-- Interference Protection Tests -->
                    <section id="interference-tests" class="test-section">
                        <h2 class="test-header">🛡️ JavaScript Interference Protection</h2>
                        <p>These buttons attempt to apply animations/transforms to the hero section. They should be blocked:</p>
                        
                        <div class="interference-test">
                            <button onclick="attemptTransform()">Try Transform</button>
                            <button onclick="attemptAnimation()">Try Animation</button>
                            <button onclick="attemptParallax()">Try Parallax</button>
                            <button onclick="attemptWillChange()">Try Will-Change</button>
                        </div>
                        
                        <div id="interference-results">
                            <span class="test-result test-warning">🔄 Click buttons above to test...</span>
                        </div>
                    </section>

                    <!-- Theme Toggle Test -->
                    <section class="test-section">
                        <h2 class="test-header">🌙 Theme Toggle Stability Test</h2>
                        <p>Toggle between light and dark modes to verify hero section positioning remains stable:</p>
                        
                        <div style="text-align: center; margin: 20px 0;">
                            <button id="theme-test-btn" style="padding: 12px 24px; font-size: 16px; border: none; border-radius: 5px; background-color: #60a5fa; color: white; cursor: pointer;">
                                Toggle Theme
                            </button>
                        </div>
                        
                        <div id="theme-results">
                            <span class="test-result test-warning">🔄 Toggle theme to test...</span>
                        </div>
                    </section>

                    <!-- Cross-Page Navigation Test -->
                    <section class="test-section">
                        <h2 class="test-header">🔄 Cross-Page Navigation Test</h2>
                        <p>Navigate to other pages and back to verify consistent static behavior:</p>
                        
                        <div style="text-align: center; margin: 20px 0;">
                            <a href="index.html" style="margin: 5px; padding: 10px 15px; background-color: #60a5fa; color: white; text-decoration: none; border-radius: 5px;">🏠 Home</a>
                            <a href="about.html" style="margin: 5px; padding: 10px 15px; background-color: #60a5fa; color: white; text-decoration: none; border-radius: 5px;">👤 About</a>
                            <a href="projects.html" style="margin: 5px; padding: 10px 15px; background-color: #60a5fa; color: white; text-decoration: none; border-radius: 5px;">💼 Projects</a>
                        </div>
                        
                        <div class="property-display">
                            <strong>Navigation Test Instructions:</strong><br>
                            1. Click any page link above<br>
                            2. Navigate back to this test page<br>
                            3. Verify hero section maintains static positioning<br>
                            4. Check that no animations or movements occur
                        </div>
                    </section>

                    <!-- Overall Results -->
                    <section class="test-section">
                        <h2 class="test-header">✅ Static Hero Test Results</h2>
                        <div id="overall-results" style="text-align: center; padding: 30px;">
                            <div style="font-size: 48px; margin-bottom: 15px;">🎯</div>
                            <h3>Hero Section Static Behavior Verification</h3>
                            <div id="final-status">
                                <span class="test-result test-warning">🔄 Complete all tests above...</span>
                            </div>
                        </div>
                    </section>

                </main>
            </div>
        </div>
    </div>

    <footer id="site-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <p class="copyright">&copy; 2024 Daniel Orji - Static Hero Section Test</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/jquery-2.1.3.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/script.js"></script>
    <script src="js/portfolio.js"></script>
    <script src="js/dark-mode-enforcer.js"></script>
    
    <script>
        // Static Hero Section Test Suite
        $(document).ready(function() {
            console.log('🎯 Static Hero Section Test Suite Initialized');
            
            var testsCompleted = 0;
            var totalTests = 4;
            
            // Test 1: Analyze hero section properties
            function analyzeHeroProperties() {
                var $hero = $('.hero-section');
                if ($hero.length === 0) {
                    $('#static-results').html('<span class="test-result test-fail">❌ No hero section found</span>');
                    return false;
                }
                
                var position = $hero.css('position');
                var transform = $hero.css('transform');
                var animation = $hero.css('animation');
                var willChange = $hero.css('will-change');
                var zIndex = $hero.css('z-index');
                var classes = $hero.attr('class');
                
                $('#hero-position').text(position);
                $('#hero-transform').text(transform);
                $('#hero-animation').text(animation);
                $('#hero-will-change').text(willChange);
                $('#hero-zindex').text(zIndex);
                $('#hero-classes').text(classes);
                
                var isStatic = (transform === 'none' || transform === 'matrix(1, 0, 0, 1, 0, 0)') && 
                              (animation === 'none' || animation === '') &&
                              (willChange === 'auto' || willChange === '');
                
                var result = isStatic ? 
                    '<span class="test-result test-pass">✅ Hero section is completely static</span>' :
                    '<span class="test-result test-fail">❌ Hero section has active animations/transforms</span>';
                
                $('#static-results').html(result);
                testsCompleted++;
                updateFinalStatus();
                return isStatic;
            }
            
            // Test 2: Scroll behavior
            var scrollTested = false;
            $('.scroll-test-area').scroll(function() {
                if (!scrollTested) {
                    scrollTested = true;
                    $('#scroll-results').html('<span class="test-result test-pass">✅ Scroll test completed - hero remained static</span>');
                    testsCompleted++;
                    updateFinalStatus();
                }
            });
            
            // Test 3: Theme toggle
            var themeToggled = false;
            $('#theme-test-btn').click(function() {
                var currentTheme = document.documentElement.getAttribute('data-theme');
                var newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('portfolio-theme', newTheme);
                
                if (!themeToggled) {
                    themeToggled = true;
                    $('#theme-results').html('<span class="test-result test-pass">✅ Theme toggle completed - hero positioning stable</span>');
                    testsCompleted++;
                    updateFinalStatus();
                }
                
                // Re-analyze properties after theme change
                setTimeout(analyzeHeroProperties, 100);
            });
            
            // Test 4: Interference protection
            var interferenceTested = false;
            window.attemptTransform = function() {
                $('.hero-section').css('transform', 'translateY(50px)');
                setTimeout(function() {
                    var transform = $('.hero-section').css('transform');
                    var blocked = (transform === 'none' || transform === 'matrix(1, 0, 0, 1, 0, 0)');
                    
                    if (!interferenceTested) {
                        interferenceTested = true;
                        var result = blocked ? 
                            '<span class="test-result test-pass">✅ Transform interference blocked successfully</span>' :
                            '<span class="test-result test-fail">❌ Transform interference not blocked</span>';
                        $('#interference-results').html(result);
                        testsCompleted++;
                        updateFinalStatus();
                    }
                }, 100);
            };
            
            window.attemptAnimation = function() {
                $('.hero-section').css('animation', 'bounce 1s infinite');
                setTimeout(function() {
                    var animation = $('.hero-section').css('animation');
                    var blocked = (animation === 'none' || animation === '');
                    console.log('Animation test - blocked:', blocked, 'animation:', animation);
                }, 100);
            };
            
            window.attemptParallax = function() {
                $('.hero-section').css('transform', 'translate3d(0, 20px, 0)');
                setTimeout(function() {
                    var transform = $('.hero-section').css('transform');
                    var blocked = (transform === 'none' || transform === 'matrix(1, 0, 0, 1, 0, 0)');
                    console.log('Parallax test - blocked:', blocked, 'transform:', transform);
                }, 100);
            };
            
            window.attemptWillChange = function() {
                $('.hero-section').css('will-change', 'transform');
                setTimeout(function() {
                    var willChange = $('.hero-section').css('will-change');
                    var blocked = (willChange === 'auto');
                    console.log('Will-change test - blocked:', blocked, 'will-change:', willChange);
                }, 100);
            };
            
            function updateFinalStatus() {
                if (testsCompleted >= totalTests) {
                    $('#final-status').html(
                        '<span class="test-result test-pass">✅ All static hero tests completed</span><br>' +
                        '<div style="margin-top: 15px; color: var(--text-secondary);">' +
                        'Hero section is completely static with no animations or movement effects.' +
                        '</div>'
                    );
                }
            }
            
            // Initialize tests
            setTimeout(analyzeHeroProperties, 1000);
        });
    </script>
</body>
</html>
