/*
Portfolio JavaScript for Daniel Orji
Linux Kernel Engineer <PERSON><PERSON><PERSON>
Enhanced with Dark Mode Theme System
*/

// Apply theme immediately to prevent flash of unstyled content
(function() {
    const savedTheme = localStorage.getItem('portfolio-theme') || 'light';
    console.log('Applying theme immediately:', savedTheme);
    document.documentElement.setAttribute('data-theme', savedTheme);
})();

$(document).ready(function() {

    // Dark Mode Theme System
    const themeToggle = {
        init: function() {
            console.log('Initializing theme system...');
            this.createToggleButton();
            this.loadSavedTheme();
            this.bindEvents();
            console.log('Theme system initialized');
        },

        createToggleButton: function() {
            // Header toggle button is now in HTML, just add to mobile menu
            $('.overlay nav ul').append('<li><a href="#" id="mobile-theme-toggle">Toggle Theme</a></li>');
        },

        loadSavedTheme: function() {
            const savedTheme = localStorage.getItem('portfolio-theme') || 'light';
            console.log('Loading saved theme:', savedTheme);
            this.setTheme(savedTheme);
        },

        setTheme: function(theme) {
            console.log('Setting theme to:', theme);

            // Ensure theme is valid
            if (theme !== 'light' && theme !== 'dark') {
                theme = 'light';
            }

            // Apply theme immediately
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('portfolio-theme', theme);

            console.log('Theme applied. HTML data-theme:', document.documentElement.getAttribute('data-theme'));

            // Update toggle button state
            const toggleButton = document.getElementById('theme-toggle');
            if (toggleButton) {
                toggleButton.setAttribute('aria-label',
                    theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'
                );
            }

            // Trigger custom event for theme change
            $(document).trigger('themeChanged', [theme]);

            // Force a repaint to ensure styles are applied
            document.body.style.display = 'none';
            document.body.offsetHeight; // Trigger reflow
            document.body.style.display = '';
        },

        toggleTheme: function() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            this.setTheme(newTheme);
        },

        bindEvents: function() {
            const self = this;

            // Desktop theme toggle
            $(document).on('click', '#theme-toggle', function(e) {
                e.preventDefault();
                self.toggleTheme();
            });

            // Mobile theme toggle
            $(document).on('click', '#mobile-theme-toggle', function(e) {
                e.preventDefault();
                self.toggleTheme();
            });

            // Keyboard accessibility
            $(document).on('keydown', '#theme-toggle', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    self.toggleTheme();
                }
            });
        }
    };

    // Initialize theme system
    themeToggle.init();
    
    // Animate skill bars on scroll
    function animateSkillBars() {
        $('.skill-progress').each(function() {
            var $this = $(this);
            var skillTop = $this.offset().top;
            var skillBottom = skillTop + $this.outerHeight();
            var windowTop = $(window).scrollTop();
            var windowBottom = windowTop + $(window).height();
            
            if (skillBottom >= windowTop && skillTop <= windowBottom) {
                $this.addClass('animate');
            }
        });
    }
    
    // Fade in animations on scroll
    function fadeInOnScroll() {
        $('.fade-in, .slide-in-left, .slide-in-right').each(function() {
            var $this = $(this);
            var elementTop = $this.offset().top;
            var elementBottom = elementTop + $this.outerHeight();
            var windowTop = $(window).scrollTop();
            var windowBottom = windowTop + $(window).height();
            
            if (elementBottom >= windowTop && elementTop <= windowBottom) {
                $this.addClass('visible');
            }
        });
    }
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 80
            }, 1000);
        }
    });
    
    // Project card hover effects
    $('.project-card').hover(
        function() {
            $(this).find('.tech-badges .badge').addClass('hover-effect');
        },
        function() {
            $(this).find('.tech-badges .badge').removeClass('hover-effect');
        }
    );
    
    // Skills scroller pause on hover
    $('.skills-scroller').hover(
        function() {
            $(this).find('.skills-scroll-content').css('animation-play-state', 'paused');
        },
        function() {
            $(this).find('.skills-scroll-content').css('animation-play-state', 'running');
        }
    );
    
    // Initialize animations on page load
    animateSkillBars();
    fadeInOnScroll();
    
    // Trigger animations on scroll
    $(window).scroll(function() {
        animateSkillBars();
        fadeInOnScroll();
    });
    
    // Add loading animation to buttons
    $('.btn-cta, .btn-project').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.text();
        
        if (!$btn.hasClass('loading')) {
            $btn.addClass('loading');
            $btn.text('Loading...');
            
            setTimeout(function() {
                $btn.removeClass('loading');
                $btn.text(originalText);
            }, 2000);
        }
    });
    
    // Static hero title - NO typing effects or animations
    function initStaticHeroTitle() {
        var heroTitle = document.querySelector('.hero-title');
        if (heroTitle) {
            console.log('🎯 Hero title set to static - NO typing effects');

            // Ensure title text is immediately visible and static
            var titleText = heroTitle.textContent || heroTitle.innerText;
            heroTitle.textContent = titleText; // Set text immediately, no animation

            // Remove any animation classes or styles
            heroTitle.style.animation = 'none';
            heroTitle.style.transition = 'color 0.3s ease'; // Only allow theme color transitions

            console.log('✅ Hero title is now completely static');
        }
    }

    // Initialize static hero title
    initStaticHeroTitle();
    
    // Static hero section positioning - NO ANIMATIONS OR MOVEMENT
    function initStaticHeroPositioning() {
        var $heroSection = $('.hero-section');

        if ($heroSection.length === 0) {
            return; // No hero section on this page
        }

        console.log('🎯 Initializing static hero section - NO parallax or animations');

        // Force static positioning - completely disable all transforms and animations
        $heroSection.addClass('static-hero');

        // Remove any existing transforms and animations
        $heroSection.css({
            'transform': 'none !important',
            'will-change': 'auto',
            'animation': 'none',
            'transition': 'background-color 0.3s ease, border-color 0.3s ease', // Only allow theme transitions
            'position': 'relative',
            'z-index': '1'
        });

        // Ensure hero section remains completely stationary on scroll
        $(window).off('scroll.heroParallax'); // Remove any existing scroll handlers

        // Ensure hero section remains static on resize
        $(window).resize(function() {
            $heroSection.css({
                'transform': 'none !important',
                'will-change': 'auto',
                'animation': 'none'
            });
        });

        console.log('✅ Hero section set to completely static positioning');
    }

    // Initialize static hero positioning
    initStaticHeroPositioning();

    // Static hero section consistency across pages
    function ensureStaticHeroConsistency() {
        console.log('🎯 Ensuring static hero section consistency');

        // Force static positioning on hero section and prevent any interference
        $('.hero-section').css({
            'transform': 'none !important',
            'animation': 'none !important',
            'will-change': 'auto',
            'position': 'relative',
            'z-index': '1'
        }).addClass('static-hero');

        // Ensure other elements don't interfere
        $('#site-header').css('z-index', '100');
        $('.content-body').css('z-index', '1');
        $('#site-footer').css('z-index', '10');
        $('.overlay').css('z-index', '9999');

        // Remove any scroll event handlers that might affect hero section
        $(window).off('scroll.heroParallax scroll.heroAnimation');

        console.log('✅ Static hero section consistency enforced');
    }

    // Navigation positioning fixes
    function fixNavigationPositioning() {
        // Ensure theme toggle is properly positioned
        var $themeToggle = $('#header-theme-toggle');
        if ($themeToggle.length > 0) {
            $themeToggle.css({
                'position': 'relative',
                'z-index': '102',
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'flex-end'
            });
        }

        // Fix mobile menu positioning
        $('.overlay').css({
            'position': 'fixed',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'z-index': '9999'
        });
    }

    // Page transition positioning fixes
    function handlePageTransitions() {
        // Store scroll position before navigation
        var scrollPosition = $(window).scrollTop();

        // Enhanced page transitions with positioning fixes
        $('a:not([href^="#"]):not([target="_blank"]):not(.no-transition)').on('click', function(e) {
            var href = $(this).attr('href');

            if (href && href !== '#' && !href.startsWith('mailto:') && !href.startsWith('tel:')) {
                e.preventDefault();

                // Reset any transforms before navigation
                $('.hero-section').css('transform', 'none');

                showLoadingOverlay();

                setTimeout(function() {
                    window.location.href = href;
                }, 300); // Reduced delay for better UX
            }
        });
    }

    // Prevent any JavaScript interference with hero section
    function preventHeroInterference() {
        console.log('🛡️ Setting up hero section protection');

        // Monitor for any attempts to modify hero section
        var heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            // Create a MutationObserver to watch for style changes
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        var target = mutation.target;
                        if (target.classList.contains('hero-section') ||
                            target.closest('.hero-section')) {

                            // Reset any problematic styles immediately
                            if (target.style.transform && target.style.transform !== 'none') {
                                target.style.transform = 'none';
                                console.log('🛡️ Blocked transform on hero section element');
                            }
                            if (target.style.animation && target.style.animation !== 'none') {
                                target.style.animation = 'none';
                                console.log('🛡️ Blocked animation on hero section element');
                            }
                            if (target.style.willChange && target.style.willChange !== 'auto') {
                                target.style.willChange = 'auto';
                                console.log('🛡️ Reset will-change on hero section element');
                            }
                        }
                    }
                });
            });

            // Start observing
            observer.observe(heroSection, {
                attributes: true,
                subtree: true,
                attributeFilter: ['style']
            });

            console.log('✅ Hero section protection active');
        }
    }

    // Initialize all static hero fixes
    ensureStaticHeroConsistency();
    fixNavigationPositioning();
    handlePageTransitions();
    preventHeroInterference();
    
    // Contact form enhancement (if contact form exists)
    $('#contact-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        var originalText = $submitBtn.text();
        
        $submitBtn.text('Sending...').prop('disabled', true);
        
        // Simulate form submission
        setTimeout(function() {
            $submitBtn.text('Message Sent!').removeClass('btn-primary').addClass('btn-success');
            
            setTimeout(function() {
                $submitBtn.text(originalText).prop('disabled', false).removeClass('btn-success').addClass('btn-primary');
                $form[0].reset();
            }, 3000);
        }, 2000);
    });
    
    // Add professional loading overlay
    function showLoadingOverlay() {
        $('body').append('<div class="loading-overlay"><div class="loading-spinner"></div></div>');
    }
    
    function hideLoadingOverlay() {
        $('.loading-overlay').fadeOut(500, function() {
            $(this).remove();
        });
    }
    
    // Note: Page transitions now handled by handlePageTransitions() function above
    // This prevents duplicate event handlers
    
    // Hide loading overlay on page load
    $(window).on('load', function() {
        hideLoadingOverlay();
    });
    
    // Enhanced CSS for loading overlay with dark mode support
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.9);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                transition: background-color 0.3s ease;
            }

            [data-theme="dark"] .loading-overlay {
                background: rgba(17, 24, 39, 0.9);
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #e2e8f0;
                border-top: 4px solid #1a365d;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                transition: border-color 0.3s ease;
            }

            [data-theme="dark"] .loading-spinner {
                border-color: #374151;
                border-top-color: #2563eb;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .skill-progress.animate {
                transition: width 2s ease-in-out;
            }

            .badge.hover-effect {
                transform: scale(1.1);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }

            [data-theme="dark"] .badge.hover-effect {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
            }

            .btn-cta.loading,
            .btn-project.loading {
                opacity: 0.7;
                cursor: not-allowed;
            }

            /* Theme toggle container styles */
            .theme-toggle-container {
                display: flex;
                align-items: center;
            }

            .theme-toggle-container .theme-toggle {
                margin: 0;
                padding: 8px;
            }

            /* Mobile theme toggle styles */
            #mobile-theme-toggle {
                color: var(--text-primary) !important;
                transition: color 0.3s ease;
            }

            #mobile-theme-toggle:hover {
                color: var(--rust-accent) !important;
            }
        `)
        .appendTo('head');

    // Theme-aware animations and effects
    $(document).on('themeChanged', function(event, theme) {
        // Update any theme-specific animations or effects
        console.log('Theme changed to:', theme);

        // Refresh skill bar animations if visible
        if ($('.skill-progress.animate').length > 0) {
            setTimeout(function() {
                animateSkillBars();
            }, 300);
        }

        // Update any dynamic content colors
        updateDynamicColors(theme);
    });

    // Function to update dynamic colors based on theme
    function updateDynamicColors(theme) {
        // Update any dynamically generated content colors
        $('.dynamic-content').each(function() {
            if (theme === 'dark') {
                $(this).addClass('dark-theme');
            } else {
                $(this).removeClass('dark-theme');
            }
        });
    }
});
